<template>
  <view class="wrap">
    <web-view :src="href" class="message" v-if="href"></web-view>
  </view>
</template>

<script>
import { getTokenByUser } from '@/api/worker.js'
import { mapGetters } from 'vuex'
export default {
  data() {
    return { href: '' }
  },
  computed: { ...mapGetters(['isLogin', 'userInfo']) },
  onLoad() {
    if (this.isLogin) {
      this.loadToken()
    }
    console.log('🚀 ~ onLoad ~ this.$store:', this.$store)
  },
  watch: {
    isLogin(nVal, oVal) {
      if (nVal) {
        this.loadToken()
      }
    }
  },
  methods: {
    loadToken() {
      getTokenByUser({ type: 'worker' }).then(res => {
        console.log('🚀 ~ getTokenByUser ~ res:', res)
        const { password, clientid, username } = res.data
        const nickname = this.userInfo.nickname
        const channelCode = 'hbs119'
        const avatar = this.userInfo.avatar
        this.href = `https://mqtt.r.cdhuyun.com/h5/#/?userId=${clientid}&nickname=${nickname}&channelCode=${channelCode}&avatar=${avatar}&password=${password}`
      })
    }
  },
  onUnload() {
    uni.reLaunch({ url: '/pages/tabs/index/index' })
  }
}
</script>

<style lang="scss" scoped>
.wrap,
.message {
  width: 100%;
  height: 100%;
}
</style>
