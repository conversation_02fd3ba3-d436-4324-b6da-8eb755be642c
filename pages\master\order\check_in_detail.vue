<template>
  <view class="check-in-detail">
    <!-- 顶部状态栏 -->
    <view class="status-header" :class="detailData.type">
      <view class="status-content">
        <view class="status-title">{{ detailData.type_txt }}</view>
        <view class="status-time">{{ detailData.add_time }}</view>
      </view>
    </view>

    <!-- 主要内容区 -->
    <view class="main-content">
      <!-- 基本信息 -->
      <view class="info-section">
        <view class="info-row">
          <text class="info-label">签到人</text>
          <text class="info-value">{{ detailData.workerInfo && detailData.workerInfo.real_name }}</text>
        </view>
        <view class="info-row">
          <text class="info-label">联系电话</text>
          <text class="info-value">{{ detailData.workerInfo && detailData.workerInfo.contact_phone }}</text>
        </view>
      </view>

      <!-- 上门签到信息 -->
      <template v-if="detailData.type === 'work_start'">
        <view class="info-section">
          <view class="info-row">
            <text class="info-label">签到人数</text>
            <text class="info-value">{{ detailData.start_num }}人</text>
          </view>
          <view class="image-box" v-if="detailData.start_img">
            <text class="image-title">签到图片</text>
            <image :src="detailData.start_img" mode="aspectFill" @click="previewImage(detailData.start_img)"></image>
          </view>
        </view>
      </template>

      <!-- 下班签到信息 -->
      <template v-if="detailData.type === 'work_end'">
        <!-- 师傅人数 -->
        <view class="info-section">
          <view class="section-header">
            <text class="section-title">{{ detailData.end_content.end_num.name }}</text>
            <text class="number-value">{{ detailData.end_content.end_num.value }}人</text>
          </view>
        </view>

        <!-- 施工进度 -->
        <view class="info-section" v-if="detailData.end_content.progress_list">
          <view class="section-header">
            <text class="section-title">{{ detailData.end_content.progress_list.name }}</text>
          </view>
          <view class="progress-list">
            <view class="progress-item" v-for="(item, index) in detailData.end_content.progress_list.child" :key="index">
              <text class="item-name">{{ item.name }}</text>
              <view class="status-badge" :class="item.value[0] == 0 ? 'finished' : 'unfinished'">
                {{ getProgressStatusText(item.value) }}
              </view>
            </view>
          </view>
        </view>

        <!-- 离场确认 -->
        <view class="info-section" v-if="detailData.end_content.confirm_list">
          <view class="section-header">
            <text class="section-title">{{ detailData.end_content.confirm_list.name }}</text>
          </view>
          <view class="confirm-list">
            <view class="confirm-item" v-for="(item, index) in detailData.end_content.confirm_list.child" :key="index">
              <template v-if="item.type === 'radio'">
                <text class="item-name">{{ item.name }}</text>
                <view class="status-badge" :class="item.value === 1 ? 'finished' : 'unfinished'">
                  {{ item.value === 1 ? '已确认' : '未确认' }}
                </view>
              </template>
            </view>
          </view>

          <!-- 卫生视频 -->
          <view class="video-box" v-for="(item, index) in detailData.end_content.confirm_list.child" :key="'video-' + index">
            <template v-if="item.type === 'video'">
              <text class="video-title">{{ item.name }}</text>
              <video :src="item.value" class="video-player" controls></video>
            </template>
          </view>
        </view>

        <!-- 确认声明 -->
        <view class="confirm-section" v-if="detailData.end_content.note">
          <view class="confirm-box">
            <text class="confirm-text">{{ detailData.end_content.note.name }}</text>
            <view v-if="detailData.end_content.note.value === 1" class="confirm-badge">已确认</view>
          </view>
        </view>
      </template>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      detailData: {
        type_txt: '',
        add_time: '',
        type: '',
        workerInfo: {},
        start_img: '',
        start_num: 0,
        end_num: 0,
        end_content: null
      },
      id: '' // 签到记录ID
    }
  },
  onLoad(options) {
    if (options.id) {
      this.id = options.id
    }
    if (options.result) {
      this.detailData = JSON.parse(decodeURIComponent(options.result))
    }
  },
  methods: {
    // 预览图片
    previewImage(url) {
      uni.previewImage({ urls: [url] })
    },
    getProgressStatusText(value) {
      if (!value || !value.length) return ''
      if (value[0] == 0 || value[0] === '0') return '已完成'
      const map = { 1: '拆包', 2: '组装', 3: '调试' }
      return value.map((v) => map[v] || v).join('、')
    }
  }
}
</script>

<style lang="scss" scoped>
.check-in-detail {
  min-height: 100vh;
  background: #f5f6f7;

  .status-header {
    padding: 40rpx 32rpx;
    color: #fff;

    &.work_start {
      background: linear-gradient(135deg, #4c84ff, #3a6cff);
    }

    &.work_end {
      background: linear-gradient(135deg, #00b578, #009f6a);
    }

    .status-content {
      .status-title {
        font-size: 40rpx;
        font-weight: 600;
        margin-bottom: 8rpx;
      }

      .status-time {
        font-size: 28rpx;
        opacity: 0.9;
      }
    }
  }

  .main-content {
    margin-top: -24rpx;
    border-radius: 24rpx 24rpx 0 0;
    background: #fff;
    padding: 32rpx 32rpx 40rpx;
    min-height: 100vh;

    .info-section {
      margin-bottom: 32rpx;
      background: #fff;

      .section-header {
        margin-bottom: 24rpx;

        .section-title {
          font-size: 32rpx;
          font-weight: 600;
          color: #333;
        }

        .number-value {
          font-size: 48rpx;
          font-weight: 600;
          color: #333;
          margin-top: 16rpx;
          display: block;
        }
      }

      .info-row {
        display: flex;
        align-items: center;
        padding: 24rpx 0;
        border-bottom: 1px solid #f5f5f5;

        &:last-child {
          border-bottom: none;
        }

        .info-label {
          width: 160rpx;
          color: #999;
          font-size: 28rpx;
        }

        .info-value {
          flex: 1;
          color: #333;
          font-size: 28rpx;
        }
      }

      .image-box {
        margin-top: 24rpx;

        .image-title {
          font-size: 28rpx;
          color: #999;
          margin-bottom: 16rpx;
          display: block;
        }

        image {
          width: 240rpx;
          height: 240rpx;
          border-radius: 12rpx;
        }
      }

      .progress-list,
      .confirm-list {
        .progress-item,
        .confirm-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 24rpx 0;
          border-bottom: 1px solid #f5f5f5;

          &:last-child {
            border-bottom: none;
          }

          .item-name {
            font-size: 28rpx;
            color: #333;
          }

          .status-badge {
            padding: 4rpx 16rpx;
            border-radius: 24rpx;
            font-size: 24rpx;

            &.finished {
              background: #e8f5e9;
              color: #00b578;
            }

            &.unfinished {
              background: #fff3e0;
              color: #ff9f00;
            }
          }
        }
      }

      .video-box {
        margin-top: 32rpx;

        .video-title {
          font-size: 28rpx;
          color: #333;
          margin-bottom: 16rpx;
          display: block;
        }

        .video-player {
          width: 100%;
          height: 400rpx;
          border-radius: 12rpx;
          background: #000;
        }
      }
    }

    .confirm-section {
      margin-top: 40rpx;

      .confirm-box {
        background: #f8f9fa;
        padding: 24rpx;
        border-radius: 12rpx;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .confirm-text {
          font-size: 28rpx;
          color: #666;
          flex: 1;
        }

        .confirm-badge {
          padding: 4rpx 16rpx;
          background: #e8f5e9;
          color: #00b578;
          border-radius: 24rpx;
          font-size: 24rpx;
        }
      }
    }
  }
}
</style>
