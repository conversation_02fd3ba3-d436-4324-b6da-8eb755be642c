<template>
  <view>
    <view class="cartList" :class="cartData.iScart ? 'on' : ''">
      <view class="title acea-row row-between-wrapper">
        <view class="name">{{ $t(`已选服务`) }}</view>
        <view class="del acea-row row-middle" @click="subDel">
          <view class="iconfont icon-shanchu1"></view>
          {{ $t(`清空`) }}
        </view>
      </view>
      <view class="list">
        <view class="item acea-row row-between-wrapper" v-for="(item, index) in cartList" :key="index">
          <view class="pictrue">
            <image :src="item.slider_image[0]"></image>
          </view>
          <view class="txtPic">
            <view class="name line2" :class="item.attrStatus && item.status ? '' : 'on'">
              {{ item.store_name }}
            </view>
            <view>
              <view class="bottom acea-row row-between-wrapper">
                <view class="money">
                  <text class="num">{{ item.unit_name }}</text>
                </view>
                <view class="cartNum acea-row row-middle">
                  <uni-number-box
                    v-model="item.cart_num"
                    :min="1"
                    :max="999999"
                    :step="0.1"
                    @change="handleChang($event, index)"></uni-number-box>
                </view>
              </view>
            </view>
            <view class="delTxt acea-row row-right">
              <text @click="oneDel(item.id, index)">{{ $t(`删除`) }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
    <view class="mask" v-if="cartData.iScart" @click="closeList"></view>
  </view>
</template>

<script>
export default {
  props: {
    cartData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {}
  },
  computed: {
    cartList() {
      if (this.cartData.cartList) {
        return JSON.parse(JSON.stringify(this.cartData.cartList))
      } else {
        return []
      }
    }
  },
  mounted() {},
  methods: {
    closeList() {
      this.$emit('closeList', false)
    },
    subDel() {
      this.$emit('ChangeSubDel')
    },
    oneDel(id, index) {
      this.$emit('ChangeOneDel', id, index)
    },
    handleChang(v, item) {
      this.$emit('change', v, item)
    }
  }
}
</script>

<style lang="scss">
.mask {
  z-index: 99;
}

.cartList {
  position: fixed;
  left: 0;
  // bottom: 0;
  width: 100%;
  background-color: #fff;
  z-index: 100;
  padding: 0 30rpx 100rpx 30rpx;
  box-sizing: border-box;
  border-radius: 16rpx 16rpx 0 0;
  transform: translate3d(0, 100%, 0);
  transition: all 0.3s cubic-bezier(0.25, 0.5, 0.5, 0.9);
  bottom: 0rpx;
  bottom: calc(0rpx + constant(safe-area-inset-bottom)); ///兼容 IOS<11.2/
  bottom: calc(0rpx + env(safe-area-inset-bottom)); ///兼容 IOS>11.2/

  &.on {
    transform: translate3d(0, 0, 0);
  }

  .title {
    height: 108rpx;

    .name {
      font-size: 28rpx;
      color: #282828;
      font-weight: bold;
    }

    .del {
      font-size: 26rpx;
      color: var(--view-theme);

      .iconfont {
        margin-right: 5rpx;
        font-size: 34rpx;
      }
    }
  }

  .list {
    max-height: 720rpx;
    overflow-x: hidden;
    overflow-y: auto;

    .item {
      margin-bottom: 40rpx;

      .pictrue {
        width: 176rpx;
        height: 176rpx;
        border-radius: 16rpx;
        position: relative;

        image {
          width: 100%;
          height: 100%;
          border-radius: 16rpx;
        }

        .mantle {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background: rgba(255, 255, 255, 0.65);
          border-radius: 16rpx;
        }
      }

      .txtPic {
        width: 486rpx;

        .name {
          font-size: 28rpx;
          color: #282828;

          &.on {
            color: #a3a3a3;
          }
        }

        .noBnt {
          width: 126rpx;
          height: 44rpx;
          background: rgba(242, 242, 242, 1);
          border-radius: 22rpx;
          text-align: center;
          line-height: 44rpx;
          font-size: 24rpx;
          color: #a3a3a3;
          margin-top: 10rpx;
        }

        .delTxt {
          margin-top: 48rpx;
          font-size: 24rpx;
          color: #e93323;

          text {
            width: 70rpx;
            height: 50rpx;
            text-align: center;
            line-height: 50rpx;
          }
        }

        .info {
          font-size: 23rpx;
          color: #989898;
          margin-top: 5rpx;
        }

        .bottom {
          margin-top: 11rpx;

          .money {
            font-weight: bold;
            font-size: 26rpx;
            color: var(--view-priceColor);

            .num {
              font-size: 26rpx;
            }
          }

          .cartNum {
            font-weight: bold;

            .num {
              font-size: 34rpx;
              color: #282828;
              width: 120rpx;
              text-align: center;
            }

            .reduce {
              color: #282828;
              font-size: 24rpx;
              width: 60rpx;
              height: 60rpx;
              text-align: center;
              line-height: 60rpx;
            }

            .plus {
              color: #282828;
              font-size: 24rpx;
              width: 60rpx;
              height: 60rpx;
              text-align: center;
              line-height: 60rpx;
            }
          }
        }
      }
    }
  }
}
</style>
