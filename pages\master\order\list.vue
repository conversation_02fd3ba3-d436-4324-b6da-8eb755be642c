<template>
  <view class="order-detail" :style="colorStyle" v-if="orderInfo.id">
    <!-- 订单状态 -->
    <view class="status-section">
      <text class="status-text-title">{{ orderInfo.status_text_worker }}</text>
      <text class="order-no">订单号：{{ orderInfo.order_no }}</text>
    </view>

    <!-- 联系人信息 -->
    <view class="info-card">
      <view class="card-title">联系人信息</view>
      <view class="info-item">
        <text class="label">姓名：</text>
        <text class="value">{{ orderInfo.real_name }}</text>
      </view>
      <view class="info-item">
        <text class="label">电话：</text>
        <text class="value">{{ orderInfo.user_phone }}</text>
      </view>
      <view class="info-item">
        <text class="label">地址：</text>
        <text class="value">
          {{ orderInfo.province_name }}{{ orderInfo.city_name }}{{ orderInfo.district_name }}{{ orderInfo.user_address }}
        </text>
      </view>
    </view>

    <!-- 主订单信息 -->
    <view class="info-card">
      <view class="card-title">订单信息</view>
      <view class="info-item">
        <text class="label">订单状态：</text>
        <text class="value status-tag">{{ getStatusText(orderInfo.status) }}</text>
      </view>
      <view class="info-item">
        <text class="label">下单时间：</text>
        <text class="value">{{ orderInfo.add_time }}</text>
      </view>
      <view class="info-item" v-if="orderInfo.hope_time">
        <text class="label">期望上门时间：</text>
        <text class="value">{{ orderInfo.hope_time }}</text>
      </view>
      <view class="info-item" v-if="orderInfo.mark">
        <text class="label">用户备注：</text>
        <text class="value">{{ orderInfo.mark }}</text>
      </view>
      <view class="info-item" v-if="orderInfo.is_settlement === 1">
        <text class="label">结算金额：</text>
        <text class="value price-text">￥{{ orderInfo.settlement_price }}</text>
      </view>
    </view>

    <!-- 子订单列表 -->
    <view class="info-card" v-if="orderInfo.children_order_list && orderInfo.children_order_list.length">
      <view class="card-title">服务进度</view>
      <view
        v-for="(child, index) in orderInfo.children_order_list"
        :key="child.id"
        class="child-order"
        @click="goToChildOrderDetail(child.id)"
      >
        <view class="child-order-content">
          <view class="child-order-main">
            <view class="child-order-header">
              <text class="child-order-index" v-if="child.is_confirm !== 0">追</text>
              <text class="child-order-index" v-else>{{ index + 1 }}</text>
              <text class="child-order-title">{{ child.order_no }}</text>
            </view>
            <view class="child-order-status" v-if="child.is_confirm !== 0">
              <text class="status-tag" :class="child.is_confirm == 1 ? 'tag-green' : 'tag-orange'">
                {{ child.is_confirm === 1 ? '用户已确认' : '用户未确认' }}
              </text>
            </view>
            <view class="child-order-status flex-between-center">
              <text
                class="status-tag"
                :class="{
                  'tag-gray': child.status === 0,
                  'tag-blue': [1, 2, 3, 4].includes(child.status),
                  'tag-green': child.status === 9
                }"
              >
                {{ child.status_text_worker }}
              </text>
              <view class="add-time">{{ child.add_time }}</view>
            </view>
          </view>
          <view class="child-order-right">
            <view class="arrow-right"></view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import colors from '@/mixins/color'

export default {
  mixins: [colors],

  data() {
    return {
      orderInfo: {}
    }
  },
  computed: {
    showActionButtons() {
      //   return [1, 3, 4].includes(this.orderInfo.status);
      return [3].includes(this.orderInfo.status)
    }
  },
  methods: {
    getStatusText(status) {
      const statusMap = {
        0: '待付款',
        1: '已支付待接单',
        2: '未支付待接单',
        3: '已接单待上门',
        4: '已上门服务中',
        9: '服务完成'
      }
      return statusMap[status] || '未知状态'
    },
    getStatusClass(status) {
      const classMap = {
        0: 'status-waiting',
        1: 'status-processing',
        2: 'status-processing',
        3: 'status-processing',
        4: 'status-processing',
        9: 'status-completed'
      }
      return classMap[status] || 'status-waiting'
    },
    goToChildOrderDetail(id) {
      uni.navigateTo({
        url: `/pages/master/order/index?id=${id}`
      })
    }
  },
  onLoad(options) {
    this.orderInfo = JSON.parse(options.orderInfo)
  }
}
</script>

<style lang="scss" scoped>
.order-detail {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 120rpx;
}

.status-section {
  background-color: var(--view-theme);
  padding: 40rpx 30rpx;
  color: #fff;

  .status-text-title {
    font-size: 36rpx;
    font-weight: bold;
    margin-bottom: 20rpx;
    display: block;
  }

  .order-no {
    font-size: 28rpx;
    opacity: 0.9;
  }
}

.info-card {
  background-color: #fff;
  margin: 20rpx;
  border-radius: 12rpx;
  padding: 30rpx;

  .card-title {
    font-size: 32rpx;
    font-weight: bold;
    margin-bottom: 20rpx;
    border-bottom: 1px solid #eee;
    padding-bottom: 20rpx;
  }
}

.info-item {
  display: flex;
  margin-bottom: 20rpx;
  font-size: 28rpx;

  .label {
    color: #666;
    width: 200rpx;
  }

  .value {
    color: #333;
    flex: 1;
  }
}

.child-order {
  margin-bottom: 20rpx;
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);

  &:active {
    background-color: #f9f9f9;
  }

  &:last-child {
    margin-bottom: 0;
  }
}

.child-order-content {
  padding: 24rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
}

.child-order-main {
  flex: 1;
  margin-right: 40rpx;
}

.child-order-header {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.child-order-index {
  width: 40rpx;
  height: 40rpx;
  background: var(--view-theme);
  color: #fff;
  border-radius: 20rpx;
  font-size: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16rpx;
}

.child-order-title {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
}

.child-order-status {
  margin: 16rpx 0;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 12rpx;
}

.status-tag {
  display: inline-flex;
  align-items: center;
  padding: 4rpx 16rpx;
  font-size: 24rpx;
  border-radius: 4rpx;

  &.tag-gray {
    background: #f5f5f5;
    color: #999;
  }

  &.tag-blue {
    background: rgba(24, 144, 255, 0.1);
    color: #1890ff;
  }

  &.tag-green {
    background: #f6ffed;
    color: #52c41a;
  }

  &.tag-orange {
    background: #fff7e6;
    color: #fa8c16;
  }
}

.child-order-info {
  margin-top: 12rpx;

  .info-value {
    font-size: 26rpx;
    color: #666;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
  }
}

.child-order-right {
  display: flex;
  align-items: center;
  padding-right: 10rpx;
}

.arrow-right {
  width: 16rpx;
  height: 16rpx;
  border-top: 3rpx solid #999;
  border-right: 3rpx solid #999;
  transform: rotate(45deg);
}

.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 20rpx;
  display: flex;
  justify-content: flex-end;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  width: 100%;

  .action-button {
    width: 100%;
    background-color: #1890ff;
    color: #fff;
    padding: 20rpx 60rpx;
    border-radius: 12rpx;
    font-size: 28rpx;
  }
}

.add-time {
  font-size: 24rpx;
  color: #999;
}
</style>
