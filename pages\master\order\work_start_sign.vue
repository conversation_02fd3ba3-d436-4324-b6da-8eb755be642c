<template>
  <view class="work-start-sign" :style="colorStyle">
    <!-- 顶部导航 -->
    <view class="page-header">
      <text class="title">上门签到</text>
      <text class="subtitle">请填写上门信息完成签到</text>
    </view>

    <!-- 加载状态 -->
    <view class="loading-container" v-if="loading">
      <view class="loading-content">
        <text class="iconfont icon-loading loading-icon"></text>
        <text class="loading-text">加载中...</text>
      </view>
    </view>

    <!-- 表单内容 -->
    <view class="form-content" v-else>
      <!-- 人数输入 -->
      <view class="form-card">
        <view class="form-item">
          <view class="label-box">
            <text class="required">*</text>
            <text class="label">上门人数</text>
          </view>
          <view class="number-input-box">
            <view class="number-control minus" :class="{ disabled: start_num <= 1 }" @click="adjustNumber(-1)">
              <text class="iconfont icon-jianhao1"></text>
            </view>
            <input type="number" v-model="start_num" class="number-input" @blur="validateNumber" />
            <view class="number-control plus" @click="adjustNumber(1)">
              <text class="iconfont icon-jiahao1"></text>
            </view>
          </view>
          <text class="tip-text">请输入实际上门施工人数（至少1人）</text>
        </view>
      </view>
      <!-- 上门拍照 -->
      <view class="form-card">
        <view class="form-item">
          <view class="label-box">
            <text class="required">*</text>
            <text class="label">上门拍照</text>
          </view>
          <view class="video-item">
            <view class="video-upload" @click="chooseImage" v-if="!start_img">
              <text class="iconfont icon-xiangji"></text>
              <text class="upload-text">点击拍照</text>
            </view>
            <view class="video-preview" v-else>
              <image mode="aspectFill" :src="start_img" @click="previewImage"></image>
              <view class="video-actions">
                <button hover-class="button-hover" class="action-btn" @click="chooseImage">重新拍照</button>
                <button hover-class="button-hover" class="action-btn delete" @click="removeImage">删除</button>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 确认声明 -->
      <view class="form-card">
        <view class="form-item">
          <view class="agreement-box" @click="toggleAgreement">
            <text class="checkbox" :class="{ checked: isAgree }">
              <text class="iconfont icon-xuanzhong3" v-if="isAgree"></text>
            </text>
            <text class="agreement-text">我确认所填写内容真实有效</text>
          </view>
        </view>
      </view>

      <!-- 提示信息 -->
      <view class="notice-section">
        <text class="notice-icon iconfont icon-tishi"></text>
        <text class="notice-text">请确保信息真实有效，签到后将无法修改</text>
      </view>
    </view>

    <!-- 底部按钮 -->
    <view class="footer">
      <button
        class="submit-btn"
        :class="{ 'btn-disabled': !canSubmit || submitting }"
        @click="submitSign"
        :disabled="!canSubmit || submitting"
      >
        <text class="iconfont icon-submit" v-if="!submitting"></text>
        <text class="iconfont icon-loading loading-icon" v-else></text>
        <text>{{ submitting ? '提交中...' : '确认签到' }}</text>
      </button>
    </view>
  </view>
</template>

<script>
import colors from '@/mixins/color'
import { orderSign } from '@/api/worker'

export default {
  mixins: [colors],
  data() {
    return {
      orderId: '',
      loading: false,
      submitting: false,
      start_num: 1,
      start_img: '',
      isAgree: false
    }
  },

  computed: {
    canSubmit() {
      return this.start_num >= 1 && this.start_img && this.isAgree
    }
  },

  onLoad(options) {
    this.orderId = options.orderId
  },

  methods: {
    // 调整人数
    adjustNumber(delta) {
      let newValue = parseInt(this.start_num || 0) + delta
      if (newValue < 1) newValue = 1
      this.start_num = newValue
    },

    // 验证人数
    validateNumber() {
      let value = parseInt(this.start_num)
      if (isNaN(value) || value < 1) {
        this.start_num = 1
      }
    },

    // 选择图片
    async chooseImage() {
      this.$util.imageUploadConfirm().then((result) => {
        if (result) {
          this.$util.uploadImageOne('upload/image', (res) => {
            this.start_img = res.data.url
          })
        }
      })
    },

    // 移除图片
    removeImage() {
      this.start_img = ''
    },

    // 切换同意声明
    toggleAgreement() {
      this.isAgree = !this.isAgree
    },

    previewImage() {
      uni.previewImage({ urls: [this.start_img] })
    },
    async submitSign() {
      if (!this.canSubmit || this.submitting) return

      this.submitting = true
      try {
        const res = await orderSign({
          order_id: this.orderId,
          type: 'work_start',
          start_num: this.start_num,
          start_img: this.start_img
        })

        if (res.status === 200) {
          this.$util.Tips({ title: '签到成功' })
          // 返回上一页并刷新列表
          const pages = getCurrentPages()
          const prevPage = pages[pages.length - 2]
          if (prevPage && prevPage.$vm.loadCheckInList) {
            prevPage.$vm.loadCheckInList()
          }
          uni.navigateBack()
        } else {
          this.$util.Tips({ title: res.msg || '签到失败' })
        }
      } catch (error) {
        this.$util.Tips({ title: error.msg || '签到失败' })
      } finally {
        this.submitting = false
      }
    }
  }
}
</script>

<style lang="scss">
.work-start-sign {
  min-height: 100vh;
  background-color: #f7f8fa;
  padding-bottom: 120rpx;

  .page-header {
    background: var(--view-theme);
    color: #fff;
    padding: 60rpx 40rpx;
    position: relative;

    &::after {
      content: '';
      position: absolute;
      left: 0;
      right: 0;
      bottom: -20rpx;
      height: 20rpx;
      background: linear-gradient(to bottom, rgba(0, 0, 0, 0.05), transparent);
    }

    .title {
      font-size: 44rpx;
      font-weight: 600;
      margin-bottom: 16rpx;
      display: block;
    }

    .subtitle {
      font-size: 28rpx;
      opacity: 0.9;
    }
  }

  .loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 400rpx;

    .loading-content {
      text-align: center;

      .loading-icon {
        font-size: 48rpx;
        color: var(--view-theme);
        display: block;
        animation: rotate 1s linear infinite;
      }

      .loading-text {
        font-size: 28rpx;
        color: #666;
        margin-top: 20rpx;
      }
    }
  }

  .form-content {
    padding: 28rpx;
  }

  .form-card {
    background: #fff;
    border-radius: 24rpx;
    padding: 28rpx;
    margin-bottom: 30rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);

    &:last-child {
      margin-bottom: 0;
    }
  }

  .form-item {
    .label-box {
      display: flex;
      align-items: center;
      margin-bottom: 30rpx;

      .required {
        color: #ff4d4f;
        margin-right: 12rpx;
        font-size: 32rpx;
      }

      .label {
        font-size: 32rpx;
        color: #333;
        font-weight: 600;
      }
    }

    .number-input-box {
      display: flex;
      align-items: center;
      justify-content: space-between;
      background: #f7f8fa;
      border-radius: 12rpx;
      padding: 20rpx;
      margin-bottom: 16rpx;

      .number-control {
        width: 80rpx;
        height: 80rpx;
        border-radius: 50%;
        background: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
        transition: all 0.3s;

        &.disabled {
          opacity: 0.5;
          background: #f5f5f5;
        }

        &:active {
          transform: scale(0.95);
        }

        .iconfont {
          font-size: 40rpx;
          color: var(--view-theme);
        }
      }

      .number-input {
        flex: 1;
        text-align: center;
        font-size: 40rpx;
        font-weight: 600;
        color: #333;
        margin: 0 30rpx;
      }
    }

    .tip-text {
      font-size: 26rpx;
      color: #999;
      margin-top: 16rpx;
      display: block;
      padding-left: 10rpx;
    }
  }

  .video-item {
    .video-upload {
      width: 100%;
      height: 180rpx;
      background: #f7f8fa;
      border-radius: 12rpx;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;

      .iconfont {
        font-size: 48rpx;
        color: #999;
        margin-bottom: 12rpx;
      }

      .upload-text {
        font-size: 26rpx;
        color: #666;
      }
    }

    .video-preview {
      position: relative;
      width: 100%;
      border-radius: 12rpx;
      overflow: hidden;

      image {
        width: 100%;
        height: 360rpx;
        background: #f7f8fa;
        vertical-align: bottom;
      }

      .video-actions {
        display: flex;
        padding: 24rpx;
        background: rgba(0, 0, 0, 0.75);
        backdrop-filter: blur(10px);
        gap: 24rpx;

        .action-btn {
          flex: 1;
          height: 76rpx;
          border-radius: 38rpx;
          font-size: 28rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          transition: all 0.2s ease;
          font-weight: 500;

          &:first-child {
            background: rgba(255, 255, 255, 0.2);
            color: #fff;
            border: 1px solid rgba(255, 255, 255, 0.3);

            &:active {
              background: rgba(255, 255, 255, 0.25);
            }
          }

          &.delete {
            background: rgba(244, 81, 30, 0.9);
            color: #fff;

            &:active {
              background: rgba(244, 81, 30, 1);
            }
          }
        }
      }
    }
  }

  .agreement-box {
    display: flex;
    align-items: center;
    padding: 20rpx 0;

    .checkbox {
      width: 40rpx;
      height: 40rpx;
      border: 2rpx solid #dcdfe6;
      border-radius: 8rpx;
      margin-right: 20rpx;
      display: flex;
      align-items: center;
      justify-content: center;

      &.checked {
        background: var(--view-theme);
        border-color: var(--view-theme);

        .iconfont {
          color: #fff;
          font-size: 28rpx;
        }
      }
    }

    .agreement-text {
      font-size: 28rpx;
      color: #333;
    }
  }

  .notice-section {
    display: flex;
    align-items: center;
    padding: 20rpx;
    background: rgba(244, 81, 30, 0.1);
    border-radius: 8rpx;
    margin-top: 30rpx;

    .notice-icon {
      font-size: 32rpx;
      color: var(--view-theme);
      margin-right: 12rpx;
    }

    .notice-text {
      font-size: 24rpx;
      color: #666;
      flex: 1;
    }
  }

  .footer {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    padding: 20rpx 30rpx;
    background-color: #fff;
    box-shadow: 0 -2rpx 12rpx rgba(0, 0, 0, 0.05);

    .submit-btn {
      width: 100%;
      height: 88rpx;
      background: var(--view-theme);
      color: #fff;
      border-radius: 44rpx;
      font-size: 32rpx;
      display: flex;
      align-items: center;
      justify-content: center;

      .iconfont {
        margin-right: 12rpx;
        font-size: 36rpx;

        &.loading-icon {
          animation: rotate 1s linear infinite;
        }
      }

      &.btn-disabled {
        background: #c8c9cc;
        opacity: 0.8;
      }
    }
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
