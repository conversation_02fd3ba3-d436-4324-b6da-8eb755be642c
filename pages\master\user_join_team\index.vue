<template>
  <view class="container" :style="colorStyle">
    <!-- 顶部统计 -->
    <view class="statistics">
      <text class="title">我加入的团队</text>
      <text class="count">共 {{ total }} 个团队</text>
    </view>

    <!-- 团队列表 -->
    <view class="team-list">
      <view
        v-for="(item, index) in teamList"
        :key="index"
        class="team-item"
        @click="goToDetail(item)"
      >
        <!-- 左侧头像 -->
        <view class="avatar">
          {{ item.user_real_name.substring(0, 1) }}
        </view>

        <!-- 中间内容 -->
        <view class="content">
          <view class="main-info">
            <text class="name">{{ item.user_real_name }}的团队</text>
            <text
              :class="[
                'status-tag',
                item.is_confirm ? 'confirmed' : 'unconfirmed',
              ]"
            >
              {{ item.is_confirm ? "已确认" : "待确认" }}
            </text>
          </view>
          <view class="sub-info">
            <view class="phone">
              <uni-icons type="phone-filled" size="14"></uni-icons>
              <text>{{ item.user_phone }}</text>
            </view>
            <view class="skills">
              <uni-icons type="medal-filled" size="14"></uni-icons>
              <text>{{ item.user_experience_skill }}</text>
            </view>
          </view>
        </view>

        <!-- 右侧按钮 -->
        <view class="actions">
          <view
            v-if="!item.is_confirm"
            class="action-btn confirm"
            @click.stop="confirmJoin(item.id)"
          >
            确认
          </view>
          <view class="action-btn quit" @click.stop="quitTeam(item.id)">
            退出
          </view>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" v-if="teamList.length === 0">
      <image src="/static/images/empty.png" mode="aspectFit"></image>
      <text>暂无加入的团队</text>
    </view>
  </view>
</template>

<script>
import { getTeamJoinList, teamConfirm, teamBack } from "@/api/worker";
import colors from "@/mixins/color";

export default {
  mixins: [colors],
  data() {
    return {
      teamList: [],
      page: 1,
      limit: 20,
      total: 0,
      loading: false,
      loadend: false,
    };
  },
  onLoad() {
    this.getTeamList();
  },
  methods: {
    // 获取团队列表
    async getTeamList() {
      try {
        if (this.loading || this.loadend) return;
        this.loading = true;
        const res = await getTeamJoinList({
          page: this.page,
          limit: this.limit,
        });
        if (res.status === 200) {
          const list = res.data.list;
          this.teamList = [...list, ...this.teamList];
          this.loadend = this.limit < list.length;
          this.page++;
          this.total = res.data.count;
        }
      } catch (e) {
        this.loading = false;
        this.$util.Tips({ title: "获取列表失败" });
      }
    },

    // 确认加入团队
    async confirmJoin(id) {
      try {
        const res = await teamConfirm(id);
        if (res.status === 200) {
          this.$util.Tips({ title: "确认成功" });
          this.getTeamList();
        }
      } catch (e) {
        this.$util.Tips({ title: "确认失败" });
      }
    },

    // 退出团队
    async quitTeam(id) {
      uni.showModal({
        title: "提示",
        content: "确定要退出该团队吗？",
        success: async (res) => {
          if (res.confirm) {
            try {
              teamBack(id).then((_res) => {
                this.$util.Tips({ title: "退出成功" });
                this.getTeamList();
              });
            } catch (e) {
              this.$util.Tips({ title: "退出失败" });
            }
          }
        },
      });
    },

    // 跳转到详情页
    goToDetail(item) {
      uni.navigateTo({
        url: `./details?info=${JSON.stringify(item)}`,
      });
    },
  },
  onReachBottom() {
    this.getTeamList();
  },
};
</script>

<style lang="scss">
.container {
  min-height: 100vh;
  background-color: #f7f8fa;

  .statistics {
    padding: 32rpx 32rpx 20rpx;
    background: #fff;
    .title {
      font-size: 40rpx;
      font-weight: 600;
      color: #333;
      display: block;
    }
    .count {
      font-size: 24rpx;
      color: #999;
      margin-top: 8rpx;
      display: block;
    }
  }

  .team-list {
    padding: 0 32rpx;
    margin-top: 20rpx;

    .team-item {
      display: flex;
      align-items: center;
      padding: 24rpx;
      background: #fff;
      margin-bottom: 20rpx;
      border-radius: 12rpx;

      .avatar {
        width: 80rpx;
        height: 80rpx;
        background: var(--view-theme);
        color: #fff;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 32rpx;
        font-weight: 500;
      }

      .content {
        flex: 1;
        margin-left: 24rpx;

        .main-info {
          display: flex;
          align-items: center;

          .name {
            font-size: 32rpx;
            color: #333;
            font-weight: 500;
          }

          .status-tag {
            margin-left: 16rpx;
            font-size: 24rpx;
            padding: 4rpx 12rpx;
            border-radius: 4rpx;

            &.confirmed {
              background: #e8f7ed;
              color: #07c160;
            }

            &.unconfirmed {
              background: #fff7e6;
              color: #ff9900;
            }
          }
        }

        .sub-info {
          margin-top: 12rpx;
          font-size: 26rpx;
          color: #666;

          .phone,
          .skills {
            display: flex;
            align-items: center;
            margin-top: 8rpx;

            uni-icons {
              margin-right: 8rpx;
              color: #999;
            }
          }
        }
      }

      .actions {
        display: flex;
        align-items: center;
        gap: 12rpx;

        .action-btn {
          padding: 8rpx 24rpx;
          font-size: 26rpx;
          border-radius: 6rpx;
          text-align: center;

          &.confirm {
            background: #1890ff;
            color: #fff;
          }

          &.quit {
            border: 1rpx solid #ff4d4f;
            color: #ff4d4f;
          }
        }
      }
    }
  }

  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding-top: 200rpx;

    image {
      width: 240rpx;
      height: 240rpx;
    }

    text {
      margin-top: 20rpx;
      font-size: 28rpx;
      color: #999;
    }
  }
}
</style>
