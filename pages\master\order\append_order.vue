<template>
  <view class="container" :style="colorStyle">
    <!-- 去添加 -->
    <view class="add-order">
      <view class="goods-list">
        <view v-for="(item, index) in goodsList" :key="item.id" class="goods-item">
          <view class="goods-info">
            <!-- 商品图片 -->
            <image :src="item.image" mode="aspectFill" class="goods-image" />
            <!-- 商品基本信息 -->
            <view class="goods-basic-info">
              <view class="goods-name">{{ item.store_name }}</view>
            </view>
            <!-- 数量选择 -->
            <view class="quantity-selector">
              <view class="quantity-control">
                <uni-number-box
                  v-model="item.cart_num"
                  :min="1"
                  :max="999999"
                  :step="0.1"
                  @change="handleChangeCartNum(item.id, $event)"
                ></uni-number-box>
              </view>
              <view class="quantity-label">{{ item.unit_name }}</view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部按钮 -->
    <view class="footer">
      <button class="btn btn-add" hover-class="button-hover" @click="handleAddGoods">添加商品</button>
      <button class="btn btn-settle" hover-class="button-hover" @click="handleSettle">结算</button>
    </view>
  </view>
</template>

<script>
import colors from '@/mixins/color'
export default {
  mixins: [colors],
  data() {
    return {
      goodsList: [
        {
          id: 5,
          image: 'http://hbs.7942a14976af3d19.png',
          slider_image: [
            'http://hbs.my/uploads/attach/2025/02/20250224/3aead988205cc8597942a14976af3d19.png',
            'http://hbs.my/uploads/attach/2025/02/20250224/bbb2d5b08bee48d5d00fbc07374ebeb6.png'
          ],
          store_name: '测试服务1',
          store_info: '测试服务1，测试服务1，测试服务1测试服务1',
          cate_id: [10, 12, 42],
          unit_name: '平米',
          sort: 99,
          sales: 0,
          is_show: 1,
          is_index: 1,
          add_time: 1744099772,
          is_del: 0,
          content:
            '<p>20</p>fhgfghf法国复合管<p>共和国</p><p><img style="max-width:100%;height:auto;display:block;margin-top:0;margin-bottom:0;"  src="http://hbs.my/uploads/attach/2025/02/20250224/3aead988205cc8597942a14976af3d19.png"/></p>',
          cart_num: 1
        }
      ]
    }
  },
  methods: {
    handleChangeCartNum(id, num) {
      const index = this.goodsList.findIndex(item => item.id === id)
      if (index !== -1) {
        this.goodsList[index].cart_num = num
      }
    },
    handleAddGoods() {
      // 跳转到商品选择页面
      uni.navigateTo({ url: '/pages/master/goods/goods' })
    },
    handleSettle() {
      // 处理结算逻辑
      const selectedGoods = this.goodsList.map(item => ({
        id: item.id,
        quantity: item.cart_num
      }))
      console.log('结算商品：', selectedGoods)
      // TODO: 实现结算逻辑
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  padding-bottom: 120rpx; // 为底部按钮留出空间
}

.add-order {
  flex: 1;
  padding: 20rpx;
  background-color: #fff;
  border-radius: 10rpx;
  margin: 20rpx;
}

.goods-list {
  .goods-item {
    border-bottom: 1rpx solid #eee;
    padding: 20rpx 0;

    &:last-child {
      border-bottom: none;
    }
  }
}

.goods-info {
  display: flex;
  align-items: flex-start;

  .goods-image {
    width: 180rpx;
    height: 180rpx;
    border-radius: 8rpx;
    margin-right: 20rpx;
  }

  .goods-basic-info {
    flex: 1;
    .goods-name {
      font-size: 28rpx;
      font-weight: bold;
      margin-bottom: 10rpx;
    }
    .goods-unit {
      font-size: 24rpx;
      color: #999;
    }
  }
}

.quantity-selector {
  display: flex;
  align-items: center;
  margin-top: 20rpx;

  .quantity-label {
    font-size: 26rpx;
    margin-right: 20rpx;
  }
}

.footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100rpx;
  background-color: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30rpx;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);

  .btn {
    flex: 1;
    height: 70rpx;
    line-height: 70rpx;
    padding: 0 40rpx;
    border-radius: 35rpx;
    font-size: 28rpx;

    &.btn-add {
      background-color: #f5f5f5;
      color: #333;
      margin-right: 20rpx;
    }

    &.btn-settle {
      background-color: var(--view-theme);
      color: #fff;
    }
  }
}
</style>
