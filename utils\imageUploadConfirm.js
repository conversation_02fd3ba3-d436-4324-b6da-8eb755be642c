export function imageUploadConfirm() {
  return new Promise((resolve, reject) => {
    const start_confirm = uni.getStorageSync('start_confirm')
    if (start_confirm === 'yes') {
      resolve(true)
      return
    }
    uni.showModal({
      title: '系统提示',
      content:
        '确定使用您手机的摄像头和读取相册权限，上传开工图片吗？ 如果您已禁用(点击[同意上传]后无法上传)，请到手机设置授权本APP的摄像头和读取相册权限',
      showCancel: true,
      cancelColor: '#0087fc',
      confirmColor: '#fa1722',
      cancelText: '不同意',
      confirmText: '同意上传',
      success: (res) => {
        if (res.confirm) {
          uni.setStorageSync('start_confirm', 'yes')
          resolve(true)
        } else if (res.cancel) {
          resolve(false)
        }
      },
      fail: (err) => {
        reject(err)
      }
    })
  })
}
