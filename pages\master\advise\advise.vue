<template>
	<view class="container" :style="colorStyle">

		<form @submit="submitStartWork">
			<view class='addAddress'>
				<view class='list'>
					<view class='item acea-row row-between-wrapper'>
						<view class='name'><text class="tipsred">*</text>{{$t(`联系人`)}}</view>
						<input type='text' :placeholder='$t(`请输入联系人`)' name='real_name' v-model="adviseInfo.name"
							placeholder-class='placeholder'></input>
					</view>
					<view class='item acea-row row-between-wrapper'>
						<view class='name'><text class="tipsred">*</text>{{$t(`联系电话`)}}</view>
						<input type='number' :placeholder='$t(`请输入联系电话`)' name="phone" v-model='adviseInfo.phone'
							placeholder-class='placeholder' pattern="\d*"></input>
					</view>
					<view class='item acea-row row-between-wrapper'>
						<view class='name'><text class="tipsred">*</text>{{$t(`意见反馈`)}}</view>
						<textarea type='textarea' :placeholder='$t(`请输入意见反馈详情`)' name="content"
							v-model='adviseInfo.content' placeholder-class='placeholder' maxlength="200"></textarea>
					</view>
				</view>
			</view>
			<!-- 图片上传卡片 -->
			<view class="card upload-section">
				<view class="card-title">
					<text>相关图片</text>
					<text class="subtitle">（最多上传3张图片）</text>
				</view>

				<view class="image-list">
					<view class="image-item" v-for="(item, index) in attachmentImage" :key="index">
						<image :src="item" mode="aspectFill" @tap="previewImage(index)"></image>
						<view class="delete-btn" @tap.stop="deleteImage(index)">
							<text class="iconfont icon-guanbi"></text>
						</view>
					</view>
					<view class="upload-btn" @tap="chooseImage" v-if="attachmentImage.length < 3">
						<text class="iconfont icon-xiangji"></text>
						<text class="tip">上传图片</text>
					</view>
				</view>
				<text class="tips" v-if="attachmentImage.length > 3">请最多上传3张图片</text>
			</view>

			<!-- 提交按钮 -->
			<view class="bottom-btn-wrap">
				<button class="submit-btn" :disabled="attachmentImage.length > 3" @click="submitStartWork"
					hover-class="button-hover">
					提交
				</button>
			</view>
		</form>
	</view>
</template>

<script>
	import colors from '@/mixins/color.js'
	import {
		adviseAdd
	} from '@/api/customer.js'

	export default {
		mixins: [colors],
		data() {
			return {
				adviseInfo: {
					name: '',
					phone: '',
					content: '',
					images: '',
				},
				attachmentImage: []
			}
		},
		onLoad(options) {
			if (options.orderInfo) {
				this.orderInfo = JSON.parse(options.orderInfo)
			}
		},
		methods: {
			chooseImage() {
				let that = this
				let advise_confirm = uni.getStorageSync('advise_confirm');
				if (advise_confirm === 'yes') {
					that.$util.uploadImageChange('upload/image', res => {
						that.attachmentImage.push(res.data.url)
					})
					return true;
				}
				uni.showModal({
					title: '系统提示',
					content: '确定使用您手机的摄像头和读取相册权限，上传图片吗？ 如果您已禁用(点击[同意上传]后无法上传)，请到手机设置授权本APP的摄像头和读取相册权限',
					showCancel: true,
					cancelColor: '#0087fc',
					confirmColor: '#fa1722',
					cancelText: '不同意',
					confirmText: '同意上传',
					success: (res) => {
						if (res.confirm) {
							console.log('用户点击确定');
							uni.setStorageSync('advise_confirm', 'yes');
							that.$util.uploadImageChange('upload/image', res => {
								that.attachmentImage.push(res.data.url)
							})
						} else if (res.cancel) {
							console.log('用户点击取消');
						}
					}
				});
			},
			deleteImage(index) {
				uni.showModal({
					title: '提示',
					content: '确定要删除这张图片吗？',
					success: res => {
						if (res.confirm) {
							this.attachmentImage.splice(index, 1)
						}
					}
				})
			},
			previewImage(index) {
				uni.previewImage({
					urls: this.attachmentImage,
					current: index
				})
			},
			async submitStartWork() {
				if (this.adviseInfo.name == '') {
					this.$util.Tips({
						title: '联系人不能为空'
					})
					return
				}
				if (this.adviseInfo.phone == '') {
					this.$util.Tips({
						title: '联系电话不能为空'
					})
					return
				}
				if (this.adviseInfo.content == '') {
					this.$util.Tips({
						title: '意见反馈不能为空'
					})
					return
				}
				if (this.attachmentImage.length > 3) {
					this.$util.Tips({
						title: '最多上传3张图片'
					})
					return
				}
				if (this.attachmentImage.length > 0) {
					this.adviseInfo.images = this.attachmentImage.join(',')
				}

				try {
					uni.showLoading({
						title: '提交中...'
					})
					// TODO: 调用提交接口
					adviseAdd(this.adviseInfo)
						.then(res => {
							this.$util.Tips({
								title: res.msg
							})
							setTimeout(() => {
								uni.navigateBack()
							}, 1500)
						})
						.catch(err => {
							this.$util.Tips({
								title: err || '提交失败，请重试'
							})
						})
				} catch (e) {
					this.$util.Tips({
						title: '提交失败，请重试'
					})
				} finally {
					uni.hideLoading()
				}
			}
		}
	}
</script>

<style lang="scss">
	page {
		background: #f5f5f5;
		min-height: 100%;
	}

	.container {
		padding: 20rpx;
		padding-bottom: 140rpx;
	}


	.addAddress .list {
		background-color: #fff;
	}

	.addAddress .list .item {
		padding: 30rpx;
		border-top: 1rpx solid #eee;
	}

	.addAddress .list .item .name {
		width: 195rpx;
		font-size: 30rpx;
		color: #333;
		padding-bottom: 30rpx;
	}

	.addAddress .list .item .address {
		// width: 412rpx;
		flex: 1;
		margin-left: 20rpx;
	}

	.addAddress .list .item input {
		width: 475rpx;
		font-size: 30rpx;
	}

	.addAddress .list .item .placeholder {
		color: #ccc;
	}

	.addAddress .list .item picker {
		width: 475rpx;
	}

	.addAddress .list .item picker .picker {
		width: 410rpx;
		font-size: 30rpx;
	}

	.addAddress .list .item picker .iconfont {
		font-size: 43rpx;
	}

	.addAddress .default {
		padding: 0 30rpx;
		height: 90rpx;
		background-color: #fff;
		margin-top: 23rpx;
	}

	.addAddress .default checkbox {
		margin-right: 15rpx;
	}

	.addAddress .keepBnt {
		width: 690rpx;
		height: 86rpx;
		border-radius: 50rpx;
		text-align: center;
		line-height: 86rpx;
		margin: 50rpx auto;
		font-size: 32rpx;
		color: #fff;
	}

	.addAddress .wechatAddress {
		width: 690rpx;
		height: 86rpx;
		border-radius: 50rpx;
		text-align: center;
		line-height: 86rpx;
		margin: 0 auto;
		font-size: 32rpx;
		color: var(--view-theme);
		border: 1px solid var(--view-theme);
	}

	.card {
		background: #fff;
		border-radius: 12rpx;
		padding: 24rpx;
		margin-bottom: 20rpx;

		.card-title {
			font-size: 32rpx;
			font-weight: 500;
			color: #333;
			margin-bottom: 24rpx;

			.subtitle {
				font-size: 24rpx;
				color: #999;
				font-weight: normal;
				margin-left: 12rpx;
			}
		}
	}

	.order-info {
		.info-list {
			.info-item {
				display: flex;
				margin-bottom: 20rpx;
				align-items: flex-start;

				&:last-child {
					margin-bottom: 0;
				}

				.label {
					color: #666;
					width: 140rpx;
					font-size: 28rpx;
					flex-shrink: 0;
				}

				.value {
					flex: 1;
					color: #333;
					font-size: 28rpx;
				}
			}
		}
	}

	.upload-section {
		.image-list {
			display: grid;
			grid-template-columns: repeat(3, 1fr);
			column-gap: 16rpx;
			row-gap: 16rpx;

			.image-item,
			.upload-btn {
				width: 100%;
				height: 216rpx;
				position: relative;
				border-radius: 8rpx;
				overflow: hidden;
			}

			.image-item {
				image {
					width: 100%;
					height: 100%;
				}

				.delete-btn {
					position: absolute;
					right: 0;
					top: 0;
					width: 44rpx;
					height: 44rpx;
					background: rgba(0, 0, 0, 0.6);
					display: flex;
					align-items: center;
					justify-content: center;
					border-radius: 50%;

					.iconfont {
						color: #fff;
						font-size: 24rpx;
					}
				}
			}

			.upload-btn {
				background: #f7f7f7;
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;

				.iconfont {
					font-size: 56rpx;
					color: #999;
					margin-bottom: 12rpx;
				}

				.tip {
					font-size: 26rpx;
					color: #999;
				}
			}
		}

		.tips {
			color: var(--view-theme);
			font-size: 24rpx;
			margin-top: 20rpx;
			display: block;
		}
	}

	.tipsred {
		color: var(--view-theme);
		font-size: 24rpx;
	}

	.bottom-btn-wrap {
		position: fixed;
		left: 0;
		right: 0;
		bottom: 0;
		background: #fff;
		padding: 20rpx 40rpx;
		padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
		box-shadow: 0 -2rpx 8rpx rgba(0, 0, 0, 0.05);

		.submit-btn {
			height: 88rpx;
			line-height: 88rpx;
			font-size: 32rpx;
			border-radius: 44rpx;
			background: var(--view-theme);
			color: #fff;

			&:disabled {
				background: #ccc !important;
			}
		}
	}
</style>