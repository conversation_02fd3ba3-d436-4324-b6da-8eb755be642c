<template>
  <view class="container" :style="colorStyle">
    <!-- 顶部状态切换 -->
    <view class="status-tabs">
      <view
        v-for="(item, index) in orderTypes"
        :key="index"
        :class="['tab-item', currentType === item.value ? 'active' : '']"
        @tap="changeType(item.value)"
      >
        {{ item.label }}
      </view>
    </view>

    <!-- 订单列表 -->
    <view class="order-list">
      <view v-if="orderList.length > 0">
        <view class="order-item" v-for="order in orderList" :key="order.id" @click="toOrderDetail(order)">
          <!-- 订单头部 -->
          <view class="order-header">
            <view class="order-no">订单号：{{ order.order_no }}</view>
            <view class="order-status">{{ order.status_text_worker }}</view>
          </view>

          <!-- 订单内容 -->
          <view class="order-content">
            <view class="user-info">
              <view class="customer-info">
                <text class="name">{{ order.real_name }}</text>
                <text class="phone">{{ order.user_phone }}</text>
              </view>
              <view class="address-info">
                <!-- <text class="iconfont icon-dizhi"></text> -->
                <text class="address">{{ order.province_name }}{{ order.city_name }}{{ order.district_name }}{{ order.user_address }}</text>
              </view>
            </view>

            <!-- 子订单信息 -->
            <!-- <view
              class="sub-orders"
              v-if="
                order.children_order_list && order.children_order_list.length
              "
            >
              <view
                class="sub-order-item"
                v-for="subOrder in order.children_order_list"
                :key="subOrder.id"
              >
                <view class="sub-order-info">
                  <text class="status">{{ subOrder.status_text_worker }}</text>
                </view>
              </view>
            </view> -->
          </view>

          <!-- 订单底部 -->
          <view class="order-footer">
            <text class="time">{{ order.add_time }}</text>
          </view>
        </view>
      </view>

      <!-- 空状态 -->
      <view v-else class="empty-state">暂无订单数据</view>

      <!-- 加载更多 -->
      <view class="loading-more" v-if="orderList.length > 0">
        {{ hasMore ? '加载中...' : '没有更多数据了' }}
      </view>
      <!--  -->
      <view class="loading-more p-32" v-if="!isLogin" @click="handleToLogin">
        <text class="red">仅限注册并认证通过的提供上门安装服务的师傅使用！</text>
      </view>
      <view class="loading-more" v-if="!isLogin" @click="handleToLogin">
        您还未登录，请先
        <text class="red">去登录并认证</text>
      </view>
    </view>
  </view>
</template>

<script>
import colors from '@/mixins/color'
import { getOrderList } from '@/api/worker'
import { mapGetters } from 'vuex'
import { toLogin } from '@/libs/login.js'
export default {
  mixins: [colors],
  data() {
    return {
      orderTypes: [
        {
          label: '全部',
          value: 0
        },
        {
          label: '待上门',
          value: 2
        },
        {
          label: '服务中',
          value: 3
        },
        {
          label: '已完成',
          value: 4
        },
        {
          label: '已取消',
          value: 5
        }
      ],
      currentType: 0,
      orderList: [],
      page: 1,
      limit: 10,
      hasMore: true
    }
  },
  computed: { ...mapGetters({ isLogin: 'isLogin' }) },
  // 开启下拉刷新
  onPullDownRefresh() {
    if (this.isLogin) {
      this.page = 1
      this.getOrderList()
    }
    setTimeout(() => {
      uni.stopPullDownRefresh()
    }, 1500)
  },
  // 触底加载更多
  onReachBottom() {
    if (this.hasMore) {
      this.page++
      this.getOrderList()
    }
  },
  onLoad() {},
  onShow() {
    if (this.isLogin) {
      this.getOrderList()
    }
  },
  methods: {
    handleToLogin() {
      toLogin()
    },
    // 切换订单类型
    changeType(type) {
      if (!this.isLogin) return this.$util.Tips({ title: '您还未登录，请先去登录' })
      this.currentType = type
      this.page = 1
      this.orderList = []
      this.hasMore = true
      this.getOrderList()
    },

    // 获取订单列表
    async getOrderList() {
      try {
        const res = await getOrderList({
          type: this.currentType,
          page: this.page,
          limit: this.limit
        })

        if (res.status === 200) {
          const { list, count } = res.data
          if (this.page === 1) {
            this.orderList = list
          } else {
            this.orderList = [...this.orderList, ...list]
          }

          this.hasMore = this.orderList.length < count
        }
      } catch (error) {
        this.$util.Tips({ title: '获取订单列表失败' })
      }
    },
    // 跳转订单详情
    toOrderDetail(order) {
      uni.navigateTo({ url: `/pages/master/order/list?orderInfo=${JSON.stringify(order)}` })
    }
  },
  //#ifdef MP
  onShareAppMessage() {
    return {
      title: '欢迎加入好帮手师傅端',
      path: '/pages/tabs/index/index'
    }
  },
  //分享到朋友圈
  onShareTimeline: function () {
    return {
      title: '欢迎加入好帮手师傅端',
      path: '/pages/tabs/index/index'
    }
  }
  //#endif
}
</script>

<style lang="scss" scoped>
.container {
  min-height: 100vh;
  background: #f5f5f5;
}

.status-tabs {
  display: flex;
  background: #fff;
  padding: 20rpx 0;
  position: sticky;
  top: 0;
  z-index: 1;

  .tab-item {
    flex: 1;
    text-align: center;
    font-size: 28rpx;
    color: #333;
    position: relative;

    &.active {
      color: var(--view-theme);
      font-weight: 500;

      &::after {
        content: '';
        position: absolute;
        bottom: -10rpx;
        left: 50%;
        transform: translateX(-50%);
        width: 40rpx;
        height: 4rpx;
        background: var(--view-theme);
        border-radius: 2rpx;
      }
    }
  }
}

.order-list {
  padding-bottom: 30rpx;
}

.order-item {
  margin: 20rpx;
  background: #fff;
  border-radius: 12rpx;
  padding: 20rpx;

  .order-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 20rpx;
    border-bottom: 1rpx solid #eee;

    .order-no {
      font-size: 26rpx;
      color: #666;
    }

    .order-status {
      font-size: 26rpx;
      color: var(--view-theme);
    }
  }

  .order-content {
    padding: 20rpx 0;

    .user-info {
      .info-item {
        margin-bottom: 10rpx;
        font-size: 28rpx;
        color: #333;

        .label {
          color: #666;
        }
      }
    }
  }

  .sub-orders {
    margin-top: 20rpx;
    padding-top: 20rpx;
    border-top: 1rpx solid #eee;

    .sub-order-item {
      display: flex;
      justify-content: space-between;
      margin-bottom: 10rpx;

      .sub-order-info {
        display: flex;
        align-items: center;

        .status {
          color: var(--view-theme);
          font-size: 26rpx;
        }
      }
    }
  }

  .order-footer {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding-top: 20rpx;
    border-top: 1rpx solid #eee;

    .time {
      font-size: 24rpx;
      color: #999;
    }
  }
}

.empty-state {
  text-align: center;
  padding: 100rpx 0;
  color: #999;
  font-size: 28rpx;
}

.loading-more {
  text-align: center;
  font-size: 24rpx;
  color: #999;
  padding: 20rpx 0;
}

.customer-info {
  margin-bottom: 16rpx;

  .name {
    font-size: 30rpx;
    font-weight: 500;
    color: #333;
    margin-right: 20rpx;
  }

  .phone {
    font-size: 28rpx;
    color: #666;
  }
}

.address-info {
  display: flex;
  align-items: flex-start;

  .icon {
    margin-right: 8rpx;
    font-size: 32rpx;
  }

  .address {
    flex: 1;
    font-size: 28rpx;
    color: #666;
    line-height: 1.4;
  }
}

.red {
  color: var(--view-theme);
}
</style>
