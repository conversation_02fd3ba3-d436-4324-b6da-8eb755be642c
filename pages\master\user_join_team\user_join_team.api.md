
    
**简要描述：** 

- 我加入的团队 - 列表

**请求URL：** 
- ` http://hbs.my/api/worker/teamJoinList `
  
**请求方式：**
- get

**参数：** 

|参数名|必选|类型|说明|
|:----    |:---|:----- |-----   |
|page |是  |string | 页 |
|limit |是  |string | 条数 |

 **返回示例**

``` 
{
    "status": 200,
    "msg": "success",
    "data": {
        "list": [
            {
                "id": 1, //成员ID
                "wuid": 3, //队长用户ID
                "uid": 3,
                "is_confirm": 0, //我是否确认：0未确认 1已确认
                "user_real_name": "jzj", //团队长名称
                "user_phone": "13666286048", //团队长联系电话
                "user_experience_skill": "中类3,大类3,中类1" //团队长服务技能
            }
        ],
        "count": 1
    }
}
```

    
**简要描述：** 

- 我加入的团队 - 确认加入

**请求URL：** 
- ` http://hbs.my/api/worker/teamConfirm/:id `
- id 是成员ID，不是成员用户UID哦
  
**请求方式：**
- post


 **返回示例**

``` 
{
    "status": 200,
    "msg": "确认成功"
}
```

    
**简要描述：** 

- 我加入的团队 - 退出团队

**请求URL：** 
- ` http://hbs.my/api/worker/teamBack/:id `
- id 是成员ID，不是成员用户UID哦
  
**请求方式：**
- post


 **返回示例**

``` 
{
    "status": 200,
    "msg": "退出成功"
}
```