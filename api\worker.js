import request from "@/utils/request.js";

/**
 * 师傅获取自己的信息
 */
export function getWorkerUserInfo() {
	return request.get("worker/userInfo");
}

/**
 * 订单 - 佣金
 */
export function getBrokerageInfo() {
	return request.get("worker/brokerageInfo");
}

/**
 * 我的团队 - 成员列表
 */
export function getTeamList(data) {
	return request.get("worker/teamList", data);
}
/**
 * 我的团队 - 添加成员
 */
export function addTeamMember(data) {
	return request.post("worker/teamAdd", data);
}
/**
 * 我的团队 - 删除成员
 */
export function delTeamMember(id) {
	return request.post(`worker/teamDel/${id}`);
}

/**
 * 我的团队 - 添加成员
 */
export function getTeamJoinList(data) {
	return request.get("worker/teamJoinList", data);
}

/**
 * 我加入的团队 - 确认加入
 */
export function teamConfirm(id) {
	return request.post(`worker/teamConfirm/${id}`);
}

/**
 * 我加入的团队 - 退出团队
 */
export function teamBack(id) {
	return request.post(`worker/teamBack/${id}`);
}

/**
 * 订单 - 列表
 */
export function getOrderList(data) {
	return request.get("worker/orderList", data);
}

/**
 * 订单 - 详情
 */
export function getOrderDetail(id) {
	return request.get(`worker/orderDetail/${id}`);
}
/**
 * 订单 - 成员 - 添加 - 子订单才能添加
 */
export function postOrderBindTeamer(data) {
	return request.post(`worker/orderBindTeamer`, data);
}
/**
 * 订单 - 成员 - 列表 - 子订单才有
 */
export function getOrderTeamerList(orderId) {
	return request.get(`worker/orderTeamerList/${orderId}`);
}
/**
 * 订单 - 成员 - 删除
 */
export function postOrderTeamerDel(orderId) {
	return request.post(`worker/orderTeamerDel/${orderId}`);
}
/**
 * 订单 - 开工
 */
export function postOrderStart(orderId, data) {
	return request.post(`worker/orderStart/${orderId}`, data);
}
/**
 * 订单 - 开工
 */
export function postOrderOver(orderId, data) {
	return request.post(`worker/orderOver/${orderId}`, data);
}

/**
 *订单 - 追加订单
 */
export function postOrderAppend(orderId, data) {
	return request.post(`worker/orderAppend/${orderId}`, data);
}

/**
 *城市列表 - 已开通城市
 */
export function getCityListOpen(data) {
	return request.get(`customer/city_list_open`, data);
}

/**
 * 完善信息 - 提交
 */
export function updateEnterpriseInfo(uid, data) {
	return request.post(`worker/updateInfo/${uid}`, data);
}
/**
 * 获取经营品类列表
 */
export function getSkillTree() {
	return request.get("worker/skillTree");
}

/**
 *服务 - 分类列表
 */
export function getCategoryList() {
	return request.get("customer/categoryTree");
}

/**
 *服务 - 列表
 */
export function getProductslist(data) {
	return request.get("customer/goodsList", data);
}
/**
 *  聊天 - 获取MQTT登录参数
 */
export function getTokenByUser(data) {
	return request.get(`worker/getTokenByUser`, data)
}

/**
 * 师傅获取自己的信息
 */
export function getUserInfo() {
	return request.get("worker/userInfo");
}

/**
 * 意见反馈提交
 */
export function adviseAdd(data) {
	return request.post(`customer/adviseAdd`, data)
}

/**
 * 设置师傅接单状态
 * @param {number} open - 0关闭接单 1开启接单
 */
export function setWorkerOpen(open) {
	return request.post(`/worker/setWorkerOpen/${open}`);
}

/**
 * 获取订单签到记录
 * @param {Object} orderId - 订单ID
 */
export function getOrderSignList(orderId, data = {}) {
	return request.get(`worker/orderSignList/${orderId}`, data)
}

/**
 * 获取签到表单数据
 */
export function getOrderSign() {
	return request.get('worker/getOrderSign')
}

/**
 * 提交签到
 * @param {Object} data
 */
export function orderSign(data) {
	return request.post('worker/orderSign', data)
}