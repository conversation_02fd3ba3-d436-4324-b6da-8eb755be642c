import request from '@/utils/request.js'

/**
 * 首页 - banner
 */
export function getIndexBanner(data) {
	return request.get(`customer/indexBanner`, data, {
		noAuth: true
	})
}

/**
 * 首页 - 按钮 - 8个服务类别
 */
export function getIndexMenu(data) {
	return request.get(`customer/indexMenu`, data, {
		noAuth: true
	})
}
/**
 * 首页 - 按钮 - 8个服务类别
 */
export function getRecommendList(data) {
	return request.get(`customer/indexGoods`, data, {
		noAuth: true
	})
}
/**
 * 服务 - 分类列表
 */
export function getCategoryTree(data) {
	return request.get(`customer/categoryTree`, data, {
		noAuth: true
	})
}
/**
 * 服务 - 分类列表
 */
export function getGoodsList(data) {
	return request.get(`customer/goodsList`, data, {
		noAuth: true
	})
}
/**
 * 服务 -  详情
 */
export function goodsDetail(id) {
	return request.get(
		`customer/goodsDetail/${id}`, {}, {
			noAuth: true
		}
	)
}
/**
 * 购物车 - 加入
 */
export function postAddCart(data) {
	return request.post(`customer/addCart`, data)
}

/**
 * 订单 - 下单确认信息
 */
export function postOrderConfirm(data) {
	return request.post(`customer/orderConfirm`, data)
}

/**
 *城市列表 - 已开通城市
 */
export function getCityListOpen(data) {
	return request.get(`customer/city_list_open`, data)
}
/**
 *订单 - 生成
 */
export function postOrderCreate(data) {
	return request.post(`customer/orderCreate`, data)
}

/**
 * 购物车 - 列表
 */
export function getCartList(data) {
	return request.get(`customer/cartList`, data)
}

/**
 * 购物车 - 修改数量
 * @param int cartId 购物车ID
 * @param int number 修改数量
 */
export function changeCartNum(cartId, number) {
	return request.post(`customer/cartChange`, {
		cart_id: cartId,
		cart_num: number
	})
}

/**
 * 购物车 - 删除
 * @param object ids join(',') 切割成字符串
 */
export function cartDel(ids) {
	if (typeof ids === 'object') ids = ids.join(',')
	return request.post('customer/cartDel', {
		cart_ids: ids
	})
}

/**
 * 订单 - 列表
 */
export function getOrderList(data) {
	return request.get(`customer/orderList`, data)
}

/**
 * 订单 - 取消
 */
export function orderCancel(id) {
	return request.post(`customer/orderCancel/${id}`)
}

/**
 * 订单 - 删除
 */
export function orderDel(id) {
	return request.post(`customer/orderDel/${id}`)
}

/**
 * 订单 - 详情
 */
export function getOrderDetail(id) {
	return request.get(`customer/orderDetail/${id}`)
}

/**
 * 订单 - 支付
 */
export function postOrderPay(id) {
	return request.post(`customer/orderPay`, {
		order_id: id
	})
}
/**
 * 获取用户信息
 */
export function getUserInfo() {
	return request.get(`customer/userInfo`)
}

/**
 * 获取企业信息
 */
export function getEnterpriseInfo() {
	return request.get(`customer/userInfo`)
}

/**
 * 获取经营类型列表
 */
export function getBusinessTypes() {
	return request.get('customer/businessType')
}

/**
 * 获取经营品类列表
 */
export function getBusinessCategories() {
	return request.get('customer/businessCategoryTree')
}
/**
 * 完善信息 - 提交
 */
export function updateEnterpriseInfo(uid, data) {
	return request.post(`customer/updateInfo/${uid}`, data)
}
/**
 *订单 - 追加订单 - 不需要支付情况下的确认
 */
export function orderAppendConfirm(id) {
	return request.post(`customer/orderAppendConfirm/${id}`)
}
/**
 *  聊天 - 获取MQTT登录参数
 */
export function getTokenByUser(data) {
	return request.get(`worker/getTokenByUser`, data)
}

/**
 * 意见反馈提交
 */
export function adviseAdd(data) {
	return request.post(`customer/adviseAdd`, data)
}

/**
 * 订单 - 验收提交
 */
export function submitOrderAcceptance(data) {
	return request.post(`customer/orderComment`, data)
}

/**
 * 意见建议 - 列表
 */
export function adviseList(data) {
	return request.get(`customer/adviseList`, data)
}
/**
 * 意见建议 - 详情
 */
export function adviseDetail(id) {
	return request.get(`customer/adviseDetail/${id}`)
}

/**
 * 订单 - 编辑详情
 */
export function getOrderEditDetail(id) {
	return request.get(`customer/orderEditDetail/${id}`)
}
/**
 * 用户 - 订单 - 修改 - 确认信息
 */
export function postOrderEditConfirm(id, data) {
	return request.post(`customer/orderEditConfirm/${id}`, data)
}

/**
 * 订单 - 编辑提交
 * @param {number} id 订单ID
 * @param {object} data 编辑内容，包含address_id, goods_list, attachment_image, hope_time, mark
 */
export function postOrderEditSub(id, data) {
	return request.post(`customer/orderEditSub/${id}`, data)
}

/**
 * 用户 - 服务 - 详情 - 评论列表
 */
export function getGoodsReplyList(id, data) {
	return request.get(`customer/goodsReplyList/${id}`, data)
}