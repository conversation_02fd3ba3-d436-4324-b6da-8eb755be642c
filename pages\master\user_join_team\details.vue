<template>
  <view class="container">
    <view class="header">
      <view class="avatar">
        <text class="avatar-text">{{
          info.user_real_name.substring(0, 1)
        }}</text>
      </view>
      <text class="title">{{ info.user_real_name }}的团队</text>
      <text class="status" :class="info.is_confirm ? 'confirmed' : 'pending'">
        {{ info.is_confirm ? "已确认" : "待确认" }}
      </text>
    </view>

    <view class="info-section">
      <view class="info-item">
        <view class="info-label">
          <uni-icons type="person" size="16"></uni-icons>
          <text>姓名</text>
        </view>
        <text class="info-value">{{ info.user_real_name }}</text>
      </view>

      <view class="info-item">
        <view class="info-label">
          <uni-icons type="phone" size="16"></uni-icons>
          <text>联系电话</text>
        </view>
        <text class="info-value">{{ info.user_phone }}</text>
      </view>

      <view class="info-item skills">
        <view class="info-label">
          <uni-icons type="star" size="16"></uni-icons>
          <text>服务技能</text>
        </view>
        <view class="skill-tags">
          <text
            v-for="(skill, index) in skillList"
            :key="index"
            class="skill-tag"
          >
            {{ skill }}
          </text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      info: {},
      skillList: [],
    };
  },
  onLoad(options) {
    this.info = JSON.parse(options.info);
    if (this.info.user_experience_skill) {
      this.skillList = this.info.user_experience_skill.split(",");
    }
  },
  methods: {},
};
</script>

<style lang="scss">
.container {
  background-color: #f5f5f5;
  min-height: 100vh;

  .header {
    background-color: #ffffff;
    padding: 32rpx;
    display: flex;
    align-items: center;
    position: relative;
    margin-bottom: 20rpx;

    .avatar {
      width: 80rpx;
      height: 80rpx;
      background-color: #1890ff;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 20rpx;

      .avatar-text {
        color: #ffffff;
        font-size: 32rpx;
      }
    }

    .title {
      font-size: 32rpx;
      font-weight: 500;
      color: #333333;
    }

    .status {
      position: absolute;
      right: 32rpx;
      padding: 4rpx 16rpx;
      border-radius: 24rpx;
      font-size: 24rpx;

      &.confirmed {
        background-color: #e8f5e9;
        color: #4caf50;
      }

      &.pending {
        background-color: #fff3e0;
        color: #ff9800;
      }
    }
  }

  .info-section {
    background-color: #ffffff;
    padding: 0 32rpx;

    .info-item {
      padding: 32rpx 0;
      border-bottom: 1px solid #f0f0f0;

      &:last-child {
        border-bottom: none;
      }

      .info-label {
        display: flex;
        align-items: center;
        margin-bottom: 16rpx;
        color: #666666;

        text {
          margin-left: 12rpx;
          font-size: 28rpx;
        }
      }

      .info-value {
        font-size: 32rpx;
        color: #333333;
      }

      &.skills {
        .skill-tags {
          display: flex;
          flex-wrap: wrap;
          gap: 16rpx;

          .skill-tag {
            background-color: #f5f5f5;
            padding: 8rpx 24rpx;
            border-radius: 28rpx;
            font-size: 28rpx;
            color: #666666;
          }
        }
      }
    }
  }
}
</style>
