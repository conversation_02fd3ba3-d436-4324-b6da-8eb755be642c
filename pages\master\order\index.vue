<template>
  <view class="order-detail" v-if="orderDetail" :style="colorStyle">
    <!-- 订单基本信息 -->
    <view class="card">
      <view class="status-bar">
        <text class="status-text">{{ orderDetail.status_text_worker }}</text>
      </view>
      <view class="order-info">
        <view class="info-item">
          <text class="label">订单编号：</text>
          <text class="value">{{ orderDetail.order_no }}</text>
        </view>
        <view class="info-item">
          <text class="label">下单时间：</text>
          <text class="value">{{ orderDetail.add_time }}</text>
        </view>
        <view class="info-item" v-if="orderDetail.hope_time">
          <text class="label">期望上门时间：</text>
          <text class="value">{{ orderDetail.hope_time }}</text>
        </view>
        <view class="info-item" v-if="orderDetail.yuji_time">
          <text class="label">预计上门时间：</text>
          <text class="value">{{ orderDetail.yuji_time }}</text>
        </view>
      </view>
    </view>

    <!-- 联系人信息 -->
    <view class="card">
      <view class="card-title">联系人信息</view>
      <view class="contact-info">
        <view class="info-item">
          <text class="label">姓名：</text>
          <text class="value">{{ orderDetail.real_name }}</text>
        </view>
        <view class="info-item">
          <text class="label">电话：</text>
          <text class="value">{{ orderDetail.user_phone }}</text>
        </view>
        <view class="info-item">
          <text class="label">地址：</text>
          <text class="value">
            {{ orderDetail.province_name }}{{ orderDetail.city_name }}{{ orderDetail.district_name }}{{ orderDetail.user_address }}
          </text>
        </view>
      </view>
    </view>
    <!-- 签到区域 -->
    <view class="card sign-in-card">
      <view class="sign-in-content">
        <view class="sign-in-left">
          <text class="card-title-2">签到记录</text>
        </view>
        <view class="sign-in-right">
          <button class="check-btn" @click="goToCheckInList">
            <text>查看记录</text>
            <text class="iconfont icon-xiangyou"></text>
          </button>
        </view>
      </view>
    </view>
    <!-- 服务清单 -->
    <view class="card">
      <view class="card-title">服务订单</view>
      <view class="goods-list">
        <view v-for="(item, index) in orderDetail.goods_list" :key="index" class="goods-item">
          <image :src="item.goods_info.goodsinfo.image" mode="aspectFill" class="goods-image"></image>
          <view class="goods-info">
            <view class="goods-name">{{ item.goods_info.goodsinfo.store_name }}</view>
            <!-- <view class="goods-desc">{{ item.goods_info.goodsinfo.store_info }}</view> -->
            <view class="goods-price-info">
              <text class="price">{{ item.cart_num }}（{{ item.goods_info.goodsinfo.unit_name }}）</text>
              <!-- <text class="num">x</text> -->
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 工单图纸 -->
    <view class="card" v-if="attachmentImages.length > 0">
      <view class="card-title">工单图纸</view>
      <view class="attachment-list">
        <view
          class="attachment-item"
          v-for="(image, index) in attachmentImages"
          :key="index"
          @click="previewImage(image, attachmentImages)"
        >
          <image :src="image" mode="aspectFill" class="attachment-image"></image>
        </view>
      </view>
    </view>

    <!-- 团队成员 -->
    <view class="card team-card">
      <view class="card-header">
        <view class="left">
          <text class="card-title-2">团队成员</text>
          <text class="member-count">({{ teamList.length }}人)</text>
        </view>
        <view class="right">
          <button class="add-btn" @click="showAddMemberPopup">
            <text class="iconfont icon-add"></text>
            <text>添加</text>
          </button>
        </view>
      </view>
      <view class="team-list">
        <view v-for="(member, index) in teamList" :key="index" class="team-member">
          <view class="avatar">{{ member.user_real_name.substring(0, 1) }}</view>
          <view class="member-detail">
            <view class="top-row">
              <text class="member-name">{{ member.user_real_name }}</text>
              <text class="member-phone">{{ member.user_phone }}</text>
            </view>
            <view class="bottom-row">
              <text class="member-skills" v-if="member.user_experience_skill">技能：{{ member.user_experience_skill }}</text>
              <text class="member-skills" v-else>暂无技能</text>
            </view>
          </view>
          <view class="action-btn" @click="deleteTeamMember(member)">
            <text class="iconfont icon-shanchu"></text>
          </view>
        </view>
      </view>
    </view>
    <!--开工信息  -->
    <view class="card" v-if="orderDetail.start_time">
      <view class="card-title">开工信息</view>
      <view class="info-item">
        <text class="label">开工时间：</text>
        <text class="value">{{ orderDetail.start_time || '—' }}</text>
      </view>
      <view class="info-item">
        <text class="label">开工报备人数：</text>
        <text class="value">{{ orderDetail.start_num || '—' }}</text>
      </view>
      <view class="info-item" v-if="startImages.length">
        <text class="label">开工拍照：</text>
        <view class="start-attachment-list">
          <view class="start-attachment-item" v-for="(img, idx) in startImages" :key="idx" @click="previewImage(img, startImages)">
            <image :src="img" mode="aspectFill" class="start-attachment-image"></image>
          </view>
        </view>
      </view>
    </view>
    <!-- 完工信息 -->
    <view class="card" v-if="orderDetail.over_time">
      <view class="card-title">完工信息</view>
      <view class="info-item">
        <text class="label">完工时间：</text>
        <text class="value">{{ orderDetail.over_time || '—' }}</text>
      </view>
      <view class="info-item" v-if="overImages.length">
        <text class="label">完工照片：</text>
        <view class="over-attachment-list img-wrap">
          <view class="over-attachment-item" v-for="(img, idx) in overImages" :key="idx" @click="previewImage(img, overImages)">
            <image :src="img" mode="aspectFill" class="over-attachment-image"></image>
          </view>
        </view>
      </view>
      <view class="info-item" v-if="overVideos.length">
        <text class="label">完工视频：</text>
        <view class="over-attachment-list">
          <view class="over-attachment-item" v-for="(video, idx) in overVideos" :key="idx">
            <video :src="video" controls class="video"></video>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部操作栏 -->
    <view class="footer-bar" v-if="canStartWork || donetWork || canAppendOrder">
      <view class="footer-content">
        <button
          class="action-button start-work"
          :class="{ disabled: !canStartWork }"
          @click="startWork"
          hover-class="button-hover"
          v-if="canStartWork"
        >
          <text class="iconfont icon-start"></text>
          <text>开工确认</text>
        </button>
        <button
          class="action-button done-work"
          :class="{ disabled: !donetWork }"
          @click="handleDoneWork"
          hover-class="button-hover"
          v-if="donetWork"
        >
          <text class="iconfont icon-start"></text>
          <text>完工确认</text>
        </button>
        <button
          class="action-button append-order"
          :class="{ disabled: !canAppendOrder }"
          @click="appendOrder"
          hover-class="button-hover"
          v-if="canAppendOrder"
        >
          <text class="iconfont icon-append"></text>
          <text>追加订单</text>
        </button>
      </view>
    </view>

    <!-- 添加成员弹窗 -->
    <uni-popup ref="addMemberPopup" type="bottom">
      <view class="popup-content">
        <view class="popup-header">
          <text class="popup-title">添加团队成员</text>
          <text class="close-icon iconfont icon-close" @click="hideAddMemberPopup"></text>
        </view>
        <view class="popup-body">
          <view class="team-member-list">
            <view
              v-for="(member, index) in teamMemberList"
              :key="index"
              class="team-member-item"
              :class="{
                selected: selectedMember && selectedMember.id === member.id,
                disabled: member.inTeam || member.is_confirm !== 1
              }"
              @click="!member.inTeam && member.is_confirm === 1 && selectTeamMember(member)"
            >
              <view class="avatar">{{ member.user_real_name.substring(0, 1) }}</view>
              <view class="member-info">
                <view class="member-name">{{ member.user_real_name }}</view>
                <view class="member-phone">{{ member.user_phone }}</view>
                <view class="member-skills" v-if="member.user_experience_skill">技能：{{ member.user_experience_skill }}</view>
              </view>
              <view class="member-status">
                <text v-if="member.inTeam" class="status-tag in-team">已在团队</text>
                <text v-else-if="member.is_confirm !== 1" class="status-tag unconfirmed">未确认</text>
                <text v-else-if="selectedMember && selectedMember.id === member.id" class="iconfont icon-check"></text>
              </view>
            </view>
            <view class="empty-tip" v-if="teamMemberList.length === 0">暂无可选成员</view>
          </view>
        </view>
        <view class="popup-footer">
          <button class="cancel-btn" @click="hideAddMemberPopup">取消</button>
          <button class="confirm-btn" @click="addTeamMember" :disabled="!selectedMember">确定</button>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
import { getOrderDetail, postOrderBindTeamer, getOrderTeamerList, postOrderTeamerDel, postOrderAppend, getTeamList } from '@/api/worker.js'
import colors from '@/mixins/color'

export default {
  mixins: [colors],
  data() {
    return {
      orderId: null,
      orderDetail: null,
      teamList: [],
      teamMemberList: [],
      selectedMember: null,
      canStartWork: false,
      donetWork: false, //完工确认
      canAppendOrder: false,
      attachmentImages: [],
      startImages: [],
      overImages: [],
      overVideos: []
    }
  },

  watch: {
    orderDetail: {
      handler(newVal) {
        if (newVal && newVal.attachment_image) {
          this.attachmentImages = newVal.attachment_image.split(',').filter(img => img)
        } else {
          this.attachmentImages = []
        }
      },
      immediate: true
    }
  },

  onLoad(options) {
    this.orderId = options.id
    this.loadOrderTeamerList()
  },
  onShow() {
    this.getOrderDetail()
  },
  methods: {
    // 获取订单详情
    async getOrderDetail() {
      try {
        const res = await getOrderDetail(this.orderId)
        if (res.status === 200) {
          this.orderDetail = res.data
          console.log('🚀 ~ getOrderDetail ~  res.data:', res.data)
          this.startImages = res.data.start_image ? res.data.start_image.split(',').filter(i => i) : []
          this.overImages = res.data.over_image ? res.data.over_image.split(',').filter(i => i) : []
          this.overVideos = res.data.over_video ? res.data.over_video.split(',').filter(i => i) : []
          this.checkOrderStatus()
        }
      } catch (error) {
        this.$util.Tips({ title: '获取订单详情失败' })
      }
    },
    async loadOrderTeamerList() {
      try {
        const res = await getOrderTeamerList(this.orderId)
        if (res.status === 200) {
          this.teamList = res.data
        }
      } catch (error) {
        this.$util.Tips({ title: '获取团队成员失败' })
      }
    },

    // 检查订单状态，设置可执行操作
    checkOrderStatus() {
      // 根据订单状态设置可执行的操作
      this.canStartWork = this.orderDetail.status === 3
      this.donetWork = this.orderDetail.status === 4
      this.canAppendOrder = this.orderDetail.status === 4
    },
    // 预览图片
    previewImage(url, urls) {
      uni.previewImage({
        current: url,
        urls: urls || [url]
      })
    },

    // 显示添加成员弹窗
    async showAddMemberPopup() {
      try {
        const res = await getTeamList()
        if (res.status === 200) {
          // 标记已在团队中的成员
          this.teamMemberList = res.data.list.map(member => ({
            ...member,
            inTeam: this.teamList.some(teamMember => teamMember.id === member.uid)
          }))
        }
        this.$refs.addMemberPopup.open()
      } catch (error) {
        this.$util.Tips({ title: '获取团队成员失败' })
      }
    },

    // 隐藏添加成员弹窗
    hideAddMemberPopup() {
      this.$refs.addMemberPopup.close()
      this.selectedMember = null
    },

    // 选择团队成员
    selectTeamMember(member) {
      // 如果点击已选中的成员，则取消选中
      if (this.selectedMember && this.selectedMember.id === member.id) {
        this.selectedMember = null
      } else {
        this.selectedMember = member
      }
    },

    // 添加团队成员
    async addTeamMember() {
      if (!this.selectedMember) {
        this.$util.Tips({ title: '请选择成员' })
        return
      }

      try {
        const res = await postOrderBindTeamer({
          uid: this.selectedMember.uid,
          order_id: this.orderId
        })

        if (res.status === 200) {
          this.$util.Tips({ title: '添加成功' })
          this.hideAddMemberPopup()
          this.loadOrderTeamerList()
        }
      } catch (error) {
        this.$util.Tips({ title: error.msg || '添加失败' })
      }
    },

    // 删除团队成员
    async deleteTeamMember(item) {
      const { id, user_real_name } = item
      uni.showModal({
        title: '系统提示',
        content: `确认删除团队成员${user_real_name}吗？`,
        success: async res => {
          if (res.confirm) {
            try {
              const res = await postOrderTeamerDel(id)
              if (res.status === 200) {
                this.$util.Tips({ title: '删除成功' })
                this.loadOrderTeamerList()
              }
            } catch (error) {
              this.$util.Tips({ title: error.msg || '删除失败' })
            }
          } else if (res.cancel) {
            console.log('用户点击取消')
          }
        }
      })
    },

    // 开工确认
    async startWork() {
      uni.navigateTo({ url: `/pages/master/order/start_work?orderInfo=${JSON.stringify(this.orderDetail)}` })
    }, // 完工确认
    async handleDoneWork() {
      uni.navigateTo({ url: `/pages/master/order/completed?orderInfo=${JSON.stringify(this.orderDetail)}` })
    },

    // 追加订单
    async appendOrder() {
      // uni.showModal({
      //   title: '系统提示',
      //   content: '确定要追加订单吗',
      //   success: async _res => {
      //     if (_res.confirm) {
      //       try {
      //         const res = await postOrderAppend(this.orderId)
      //         if (res.status === 200) {
      //           this.$util.Tips({ title: '追加订单成功' })
      //           // 可以跳转到新订单详情页
      //           if (res.data.order_id) {
      //             uni.navigateTo({ url: `/pages/master/order/index?id=${res.data.order_id}` })
      //           }
      //         }
      //       } catch (error) {
      //         this.$util.Tips({ title: error.msg || '操作失败' })
      //       }
      //     } else if (_res.cancel) {
      //       console.log('用户点击取消')
      //     }
      //   }
      // })
      // 去追加订单页面
      uni.navigateTo({ url: `/pages/master/goods/goods?orderId=${this.orderId}` })
    },

    // 跳转到签到列表
    goToCheckInList() {
      uni.navigateTo({ url: `/pages/master/order/check_in_list?orderId=${this.orderId}` })
    }
  }
}
</script>

<style lang="scss">
.avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background: var(--view-theme);
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  font-weight: 500;
}

.order-detail {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 120rpx;

  .card {
    background-color: #fff;
    margin: 20rpx;
    border-radius: 12rpx;
    padding: 30rpx;

    .status-bar {
      padding-bottom: 20rpx;
      border-bottom: 1px solid #eee;

      .status-text {
        color: #ff6b00;
        font-size: 32rpx;
        font-weight: bold;
      }
    }

    .card-title {
      font-size: 32rpx;
      font-weight: bold;
      margin-bottom: 20rpx;
    }
  }

  .info-item {
    display: flex;
    margin: 16rpx 0;

    .label {
      color: #666;
      width: 180rpx;
    }

    .value {
      color: #333;
      flex: 1;
    }
  }

  .goods-list {
    .goods-item {
      display: flex;
      padding: 20rpx 0;
      border-bottom: 1px solid #eee;

      .goods-image {
        width: 160rpx;
        height: 160rpx;
        border-radius: 8rpx;
        margin-right: 20rpx;
      }

      .goods-info {
        flex: 1;

        .goods-name {
          font-size: 28rpx;
          font-weight: bold;
          margin-bottom: 10rpx;
        }

        .goods-desc {
          font-size: 24rpx;
          color: #666;
          margin-bottom: 20rpx;
        }

        .goods-price-info {
          margin-top: 40rpx;
          display: flex;
          align-items: center;

          .price {
            color: #e93323;
            font-size: 32rpx;
            font-weight: bold;
          }

          .unit {
            color: #999;
            font-size: 24rpx;
            margin-left: 8rpx;
          }

          .num {
            color: #333;
            font-size: 28rpx;
            margin-left: auto;
          }
        }
      }
    }
  }

  .attachment-list {
    display: flex;
    flex-wrap: wrap;
    margin: -10rpx;

    .attachment-item {
      width: calc(33.33% - 20rpx);
      margin: 10rpx;
      aspect-ratio: 1;
      border-radius: 8rpx;
      overflow: hidden;
      background-color: #f5f5f5;

      .attachment-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
  }

  .team-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 30rpx;

      .left {
        display: flex;
        align-items: center;

        .card-title-2 {
          font-size: 32rpx;
          font-weight: 600;
          color: #333;
        }

        .member-count {
          font-size: 24rpx;
          color: #999;
          margin-left: 8rpx;
        }
      }

      .add-btn {
        display: flex;
        align-items: center;
        background: none;
        border: none;
        padding: 0;
        font-size: 28rpx;
        color: #1890ff;

        .iconfont {
          margin-right: 4rpx;
          font-size: 32rpx;
        }
      }
    }

    .team-list {
      .team-member {
        display: flex;
        align-items: center;
        padding: 24rpx 0;
        border-bottom: 1px solid #f5f5f5;

        &:last-child {
          border-bottom: none;
        }

        .member-detail {
          flex: 1;
          margin-left: 20rpx;

          .top-row {
            display: flex;
            align-items: center;
            margin-bottom: 8rpx;

            .member-name {
              font-size: 30rpx;
              color: #333;
              font-weight: 500;
            }

            .member-phone {
              font-size: 26rpx;
              color: #999;
              margin-left: 16rpx;
            }
          }

          .bottom-row {
            .member-skills {
              font-size: 24rpx;
              color: #666;
              line-height: 1.4;
            }
          }
        }

        .action-btn {
          padding: 20rpx;
          color: #ff4d4f;

          .iconfont {
            font-size: 36rpx;
          }
        }
      }
    }
  }

  .footer-bar {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: #fff;
    padding: 20rpx 32rpx;
    box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);

    .footer-content {
      display: flex;
      justify-content: flex-end;
      gap: 24rpx;

      .action-button {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 70rpx;
        padding: 0 28rpx;
        border-radius: 44rpx;
        font-size: 28rpx;
        font-weight: 500;

        .iconfont {
          margin-right: 8rpx;
          font-size: 32rpx;
        }

        &.start-work {
          background: var(--view-theme);
          color: #fff;

          &.disabled {
            background: #ccc;
          }
        }
        &.done-work {
          background: #67c23a;
          color: #fff;

          &.disabled {
            background: #ccc;
          }
        }

        &.append-order {
          background: #fff;
          color: var(--view-theme);
          border: 2rpx solid var(--view-theme);

          &.disabled {
            color: #999;
            border-color: #ddd;
          }
        }
      }
    }
  }

  .popup-content {
    background-color: #fff;
    border-radius: 24rpx 24rpx 0 0;

    .popup-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 32rpx;
      border-bottom: 1rpx solid #f5f5f5;

      .popup-title {
        font-size: 32rpx;
        font-weight: 600;
        color: #333;
      }

      .close-icon {
        font-size: 40rpx;
        color: #999;
        padding: 10rpx;
      }
    }

    .popup-body {
      padding: 32rpx;
      max-height: 60vh;
      overflow-y: auto;

      .team-member-list {
        .team-member-item {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 24rpx;
          border-bottom: 1px solid #f5f5f5;
          background: #fff;
          border-radius: 8rpx;
          margin-bottom: 16rpx;

          &.selected {
            background: rgba(233, 51, 35, 0.05);
            border: 1px solid var(--view-theme);
          }

          &.disabled {
            opacity: 0.6;
            background: #f5f5f5;
            cursor: not-allowed;
          }

          .member-info {
            flex: 1;
            margin-left: 28rpx;

            .member-name {
              font-size: 30rpx;
              color: #333;
              font-weight: 500;
              margin-bottom: 8rpx;
            }

            .member-phone {
              font-size: 26rpx;
              color: #666;
              margin-bottom: 8rpx;
            }

            .member-skills {
              font-size: 24rpx;
              color: #999;
            }
          }

          .member-status {
            .status-tag {
              display: inline-block;
              padding: 4rpx 12rpx;
              border-radius: 4rpx;
              font-size: 24rpx;

              &.in-team {
                background: #e6f7ff;
                color: #1890ff;
              }

              &.unconfirmed {
                background: #fff1f0;
                color: #ff4d4f;
              }
            }

            .iconfont {
              color: #52c41a;
              font-size: 32rpx;
            }
          }
        }

        .empty-tip {
          text-align: center;
          color: #999;
          padding: 32rpx;
        }
      }
    }

    .popup-footer {
      display: flex;
      padding: 32rpx;
      gap: 24rpx;

      button {
        flex: 1;
        height: 88rpx;
        border-radius: 44rpx;
        font-size: 32rpx;
        font-weight: 500;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .cancel-btn {
        background: #f5f5f5;
        color: #666;
      }

      .confirm-btn {
        background: var(--view-theme);
        color: #fff;
      }
    }
  }

  .sign-in-card {
    background: #fff;
    margin: 20rpx;
    border-radius: 12rpx;
    padding: 0;

    .sign-in-content {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 30rpx;

      .sign-in-left {
        .card-title-2 {
          font-size: 32rpx;
          font-weight: 600;
          color: #333;
        }
      }

      .sign-in-right {
        .check-btn {
          display: flex;
          align-items: center;
          background: none;
          border: none;
          padding: 0;
          font-size: 28rpx;
          color: #e93323;

          &::after {
            display: none;
          }

          text {
            color: #e93323;
          }

          .iconfont {
            margin-left: 4rpx;
            font-size: 24rpx;
          }
        }
      }
    }
  }

  // 开工信息图片样式
  .start-attachment-list {
    display: flex;
  }
  .start-attachment-item {
    width: 100rpx;
    height: 100rpx;
    margin: 10rpx;
    border-radius: 8rpx;
    overflow: hidden;
    background-color: #f0f7ff;
    aspect-ratio: 1 / 1;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1rpx solid #333;
  }
  .start-attachment-image {
    width: 100%;
    height: 100%;
    border-radius: 8rpx;
  }

  // 完工信息图片/视频样式
  .over-attachment-list {
    display: flex;
    flex-wrap: wrap;
    width: 100%;
  }
  .img-wrap {
    display: flex;
    column-gap: 16rpx;
    height: 100rpx;
  }
  .over-attachment-item {
    flex: 1;
    border-radius: 8rpx;
    background-color: #f5f5f5;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .video {
    width: 100%;
    height: 200rpx;
    border-radius: 8rpx;
  }
  .over-attachment-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
    border-radius: 8rpx;
  }
}
</style>
