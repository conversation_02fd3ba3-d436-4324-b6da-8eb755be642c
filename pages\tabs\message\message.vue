<template>
  <view></view>
</template>

<script>
import { mapGetters } from 'vuex'
import { toLogin } from '@/libs/login.js'
export default {
  data() {
    return {}
  },
  computed: { ...mapGetters({ isLogin: 'isLogin' }) },
  methods: {},
  onLoad() {
    if (this.isLogin) {
      // #ifdef APP-PLUS
      plus.android.requestPermissions(['android.permission.MODIFY_AUDIO_SETTINGS', 'android.permission.RECORD_AUDIO'], function (e) {
        if (e.deniedAlways.length > 0) {
          // 权限被永久拒绝
          // 弹出提示框解释为何需要权限，引导用户打开设置页面开启
          console.log('权限被永久拒绝' + e.deniedAlways.toString())
        }
        if (e.deniedPresent.length > 0) {
          //权限被临时拒绝
          // 弹出提示框解释为何需要权限，可再次调用plus.android.requestPermissions申请权限
          console.log('权限被临时拒绝' + e.deniedPresent.toString())
        }
        if (e.granted.length > 0) {
          //权限被允许
          console.log('权限被允许：' + e.granted.toString())
          uni.navigateTo({ url: '/pages/tabs/message/index' })
        }
      })
      // #endif
      // #ifndef APP-PLUS
      uni.navigateTo({ url: '/pages/tabs/message/index' })
      // #endif
    } else {
      uni.showModal({
        title: '系统提示',
        content: '你还未登录，请先登录',
        confirmColor: '立即登录',
        cancelText: '稍后再试',
        success(res) {
          if (res.confirm) {
            toLogin()
          } else {
            uni.redirectTo({ url: '/pages/tabs/index/index' })
          }
        }
      })
    }
  },
  onShow() {
    this.$store.dispatch('USERINFO')
  }
}
</script>

<style></style>
