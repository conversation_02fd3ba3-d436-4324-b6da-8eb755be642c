<template>
  <view :style="colorStyle">
    <goodsCate3 ref="Three" :isNew="isNew" @jumpIndex="jumpIndex" @submit="submit"></goodsCate3>
  </view>
</template>

<script>
import colors from '@/mixins/color'
import goodsCate3 from './goods_cate3'
import { mapGetters } from 'vuex'
import { postOrderAppend } from '@/api/worker.js'
export default {
  computed: mapGetters(['isLogin', 'uid']),
  components: { goodsCate3 },
  mixins: [colors],
  data() {
    return {
      category: '',
      status: 0,
      version: '',
      isNew: false,
      isFooter: false,
      showBar: false,
      orderId: 0
    }
  },
  onLoad(options) {
    if (options.orderId) {
      this.orderId = options.orderId
    }
  },
  onReady() {},
  onShow() {},
  methods: {
    jumpIndex() {
      // uni.reLaunch({
      // 	url: '/pages/index/index'
      // })
    },
    submit(data) {
      postOrderAppend(this.orderId, { goods_num: data })
        .then(res => {
          console.log('res', res)
          this.$util.Tips({ title: '追加订单成功' }, () => {
            uni.reLaunch({ url: '/pages/tabs/index/index' })
          })
        })
        .catch(err => {
          console.log('err', err)
          this.$util.Tips({ title: err.msg || err })
        })
    }
  },
  onReachBottom() {
    this.$refs.Three.productslist()
  }
}
</script>
<style scoped lang="scss">
/deep/.mask {
  z-index: 99;
}
::-webkit-scrollbar {
  width: 0;
  height: 0;
  color: transparent;
  display: none;
}
</style>
