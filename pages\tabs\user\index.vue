<template>
  <view class="user-center" :style="colorStyle">
    <!-- 用户信息区域 -->
    <view class="head">
      <view class="user-card" :class="member_style == 3 ? 'unBg' : ''">
        <view class="bg"></view>
        <view class="user-info">
          <view>
            <!-- #ifndef APP-PLUS -->
            <view class="avatar-box" :class="{ on: userInfo.is_money_level }">
              <image class="avatar" :src="userInfo.avatar" v-if="userInfo.avatar" @click="goEdit()"></image>
              <image v-else class="avatar" src="/static/images/f.png" mode="" @click="goEdit()"></image>
              <view class="headwear" v-if="userInfo.is_money_level && userInfo.svip_open">
                <image src="/static/images/headwear.png"></image>
              </view>
            </view>
            <!-- #endif -->
            <!-- #ifdef APP-PLUS -->
            <view class="avatar-box" :class="{ on: userInfo.is_money_level }">
              <image class="avatar" :src="userInfo.avatar" v-if="userInfo.avatar" @click="goEdit()"></image>
              <image v-else class="avatar" src="/static/images/f.png" mode="" @click="goEdit()"></image>
              <view class="headwear" v-if="userInfo.is_money_level && userInfo.svip_open">
                <image src="/static/images/headwear.png"></image>
              </view>
            </view>
            <!-- #endif -->
          </view>
          <view class="info">
            <!-- #ifdef MP || APP-PLUS -->
            <view class="name" v-if="!userInfo.uid" @click="openAuto" style="height: 100%; display: flex; align-items: center">
              {{ $t('请点击登录') }}
            </view>
            <!-- #endif -->
            <!-- #ifdef H5 -->
            <view class="name" v-if="!userInfo.uid" @click="openAuto" style="height: 100%; display: flex; align-items: center">
              {{ $t(isWeixin ? '请点击登录' : '请点击登录') }}
            </view>
            <!-- #endif -->
            <view class="name" v-if="userInfo.uid">
              <text class="line1 nickname">{{ userInfo.nickname }}</text>
              <image class="live" :src="userInfo.vip_icon" v-if="userInfo.vip_icon"></image>
              <view class="vip" v-if="userInfo.is_money_level > 0 && userInfo.svip_open">
                <image src="/static/images/svip.png"></image>
              </view>
            </view>
            <view class="num" v-if="userInfo.phone" @click="goEdit()">
              <view class="num-txt">{{ userInfo.phone }}</view>
            </view>
          </view>
          <view class="message">
            <navigator v-if="isLogin" url="/pages/users/user_info/index" hover-class="none">
              <view class="iconfont icon-shezhi"></view>
            </navigator>
          </view>
        </view>
        <view class="num-wrapper">
          <view class="num-item" @click="goMenuPage('/pages/master/user_spread_user/index')">
            <text class="num">{{ brokerageInfo.brokerage_price || 0 }}</text>
            <view class="txt">{{ $t('当前佣金') }}</view>
          </view>
          <view class="num-item" @click="goMenuPage('/pages/master/user_spread_user/index')">
            <text class="num">{{ brokerageInfo.can_extract_money || 0 }}</text>
            <view class="txt">{{ $t('可提现佣金') }}</view>
          </view>
          <view class="num-item" @click="goMenuPage('/pages/master/user_spread_user/index')">
            <text class="num">{{ brokerageInfo.team_deposit || 0 }}</text>
            <view class="txt">{{ $t('押金') }}</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 功能列表 -->
    <view class="body">
      <!-- 接单开关板块 -->
      <view class="switch-block" v-if="isVerifiedWorker">
        <view class="switch-card">
          <view class="switch-info">
            <view class="switch-icon">
              <text class="iconfont icon-dingdan"></text>
            </view>
            <view class="switch-text">
              <text class="switch-title">接单状态</text>
              <text class="switch-desc" :class="{ 'status-on': isAcceptingOrders }">
                {{ isAcceptingOrders ? '正在接单' : '暂停接单' }}
              </text>
            </view>
          </view>
          <view class="switch-control" @click="handleOrderSwitch">
            <view class="switch" :class="{ active: isAcceptingOrders }">
              <view class="switch-handle"></view>
            </view>
          </view>
        </view>
      </view>

      <view class="function-list">
        <view class="function-item" v-for="(item, index) in functionItems" :key="index" @click="handleFunctionClick(item)">
          <view class="function-left">
            <image class="function-icon" mode="widthFix" :src="item.icon"></image>
            <text class="function-name">{{ item.name }}</text>
            <text class="function-status" v-if="item.status">{{ item.status }}</text>
          </view>
          <view class="function-right">
            <text v-if="item.tutorialUrl && item.status === '未认证'" class="tutorial-tip" @click.stop="goTutorial(item.tutorialUrl)">
              认证教程
            </text>
            <text class="iconfont icon-xiangyou"></text>
          </view>
        </view>
        <view class="function-item" @click="showAdvise">
          <image class="function-icon" mode="widthFix" src="/static/images/user-agreement.png"></image>
          <text class="function-name">意见建议</text>
          <text class="function-status">客服电话：13166296869 问题反馈</text>
          <text class="iconfont icon-xiangyou"></text>
        </view>
      </view>
    </view>
    <!--版权信息 -->
    <view class="mark">
      <!-- #ifdef APP-PLUS -->
      <view class="logOut cartcolor acea-row row-center-wrapper" @click="onUpdate">{{ $t(`检测更新`) }}</view>
      <!-- #endif -->
      <view class="version">当前版本:{{ version }}</view>
      <view class="copyright">{{ copyright }}</view>
    </view>
    <view class="uni-p-b-98"></view>
    <!-- 申请成为师傅弹窗 -->
    <uni-popup ref="masterPopup" type="center">
      <view class="popup-content master-popup">
        <view class="popup-header">
          <image class="popup-icon" src="/static/images/master-icon.png" mode="aspectFit"></image>
          <text class="popup-title">认证成为师傅</text>
          <text class="popup-subtitle">成为师傅可以接单赚取更多佣金</text>
        </view>
        <view class="popup-body">
          <view class="benefit-list">
            <view class="benefit-item">
              <text class="iconfont icon-qiandai"></text>
              <text class="benefit-text">更高收益</text>
            </view>
            <view class="benefit-item">
              <text class="iconfont icon-dingdan"></text>
              <text class="benefit-text">优先接单</text>
            </view>
            <view class="benefit-item">
              <text class="iconfont icon-tuandui"></text>
              <text class="benefit-text">组建团队</text>
            </view>
          </view>
        </view>
        <view class="popup-footer">
          <button class="popup-btn cancel" @click="closeMasterPopup">暂不申请</button>
          <button class="popup-btn confirm" @click="applyMaster">立即认证</button>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
import { mapGetters } from 'vuex'
import colors from '@/mixins/color'
import { toLogin } from '@/libs/login'
import { getWorkerUserInfo, getBrokerageInfo, setWorkerOpen } from '@/api/worker'
import { appUpdate } from '@/api/public.js'
import app_upgrade from '@/uni_modules/app-upgrade/js_sdk/index.js'
export default {
  mixins: [colors],
  data() {
    return {
      functionItems: [
        {
          name: '师傅认证',
          icon: '/static/images/personal-verify.png',
          status: '未认证',
          url: '/pages/master/personal/index',
          tutorialUrl: '/pages/tabs/news/detail?id=2'
        },
        {
          name: '我的团队',
          icon: '/static/images/company-verify.png',
          url: '/pages/master/user_team/index'
        },
        {
          name: '我加入的团队',
          icon: '/static/images/company-verify.png',
          url: '/pages/master/user_join_team/index'
        },
        {
          name: '用户协议',
          icon: '/static/images/user-agreement.png',
          url: '/pages/users/privacy/index?type=2'
        },
        {
          name: '隐私协议',
          icon: '/static/images/privacy-agreement.png',
          url: '/pages/users/privacy/index?type=1'
        },
        {
          name: '注销协议',
          icon: '/static/images/off-agreement.png',
          url: '/pages/users/user_cancellation/index'
        }
      ],
      isFirstVisit: true,
      userInfo: {},
      member_style: 3,
      brokerageInfo: {}
    }
  },
  computed: {
    ...mapGetters({
      isLogin: 'isLogin',
      copyright: 'copyright',
      version: 'version'
    }),
    // 是否是认证师傅
    isVerifiedWorker() {
      return this.userInfo.worker_info && this.userInfo.worker_info.is_worker === 1
    },
    // 是否正在接单
    isAcceptingOrders() {
      return this.isVerifiedWorker && this.userInfo.worker_info.worker_open === 1
    }
  },
  watch: {
    isLogin: {
      handler(newVal) {
        this.getUserInfo()
        this.loadBrokerageInfo()
      }
    }
  },
  onShow() {
    if (this.isLogin) {
      this.getUserInfo()
      this.checkFirstVisit()
      this.loadBrokerageInfo()
    }
  },
  methods: {
    showAdvise() {
      if (this.isLogin == false) {
        toLogin()
      } else {
        uni.navigateTo({ url: '/pages/master/advise/list' })
      }
    },
    onUpdate() {
      app_upgrade(async (versionCode) => {
        // 通过接口获取最新版本信息，具体请求不演示
        let data = {
          version: versionCode,
          name: 'shifu',
          type: uni.getSystemInfoSync().platform
        }
        console.log(' === 检测更新 === ', data)
        return appUpdate(data)
          .then((res) => {
            console.log('res', res)
            console.log('res.data.status', res.data.status)
            if (res.data.status === 0) {
              this.$util.Tips({ title: '当前已是最新版' })
              return {
                changelog: '无新版本',
                status: 0, // 0 无新版本 | 1 有新版本
                path: '' // 新apk地址
              }
            } else {
              return {
                changelog: res.data.changelog,
                status: res.data.status, // 0 无新版本 | 1 有新版本
                path: res.data.path // 新apk地址
              }
            }
          })
          .catch((err) => {
            this.$util.Tips({ title: '当前已是最新版' })
            return {
              changelog: '无新版本',
              status: 0, // 0 无新版本 | 1 有新版本
              path: '' // 新apk地址
            }
          })
      }, 1)
    },
    async getUserInfo() {
      const getWorkerStatus = (status) => {
        if (status === 0) {
          return '未认证'
        } else if (status === 1) {
          return '已认证'
        } else if (status === 2) {
          return '待审核'
        } else {
          return '未认证'
        }
      }
      try {
        // 获取用户信息
        const res = await getWorkerUserInfo()
        console.log('🚀 ~ getUserInfo ~ res:', res)
        if (res.status === 200) {
          this.userInfo = res.data
          this.functionItems[0].status = getWorkerStatus(this.userInfo.worker_info.is_worker)
        }
      } catch (error) {
        console.error('获取用户信息失败', error)
      }
    },

    checkFirstVisit() {
      const hasVisited = uni.getStorageSync('hasVisitedUserCenter')
      if (!hasVisited && this.userInfo.worker_info && this.userInfo.worker_info.is_worker === 0) {
        this.$refs.masterPopup.open()
        uni.setStorageSync('hasVisitedUserCenter', true)
      }
    },
    handleRecharge() {
      uni.navigateTo({ url: '/pages/recharge/index' })
    },
    handleFunctionClick(item) {
      if (!this.isLogin) {
        uni.showModal({
          title: '系统提示',
          content: '请先登录',
          success: function (res) {
            if (res.confirm) {
              toLogin()
            } else if (res.cancel) {
              console.log('用户点击取消')
            }
          }
        })
        return
      }

      if (item.url) {
        uni.navigateTo({ url: item.url })
      }
    },
    closeMasterPopup() {
      this.$refs.masterPopup.close()
    },
    async applyMaster() {
      this.$refs.masterPopup.close()
      uni.navigateTo({ url: '/pages/master/personal/index' })
    },
    // 编辑页面
    goEdit() {
      if (this.isLogin == false) {
        toLogin()
      } else {
        // #ifdef MP
        if (this.userInfo.is_default_avatar) {
          this.editModal = true
          return
        }
        // #endif
        uni.navigateTo({ url: '/pages/users/user_info/index' })
      }
    },
    // 打开授权
    openAuto() {
      toLogin()
    },
    async loadBrokerageInfo() {
      getBrokerageInfo().then((res) => {
        this.brokerageInfo = res.data
      })
    },
    goMenuPage(url) {
      if (!this.isLogin) {
        uni.showModal({
          title: '系统提示',
          content: '请先登录',
          success: function (res) {
            if (res.confirm) {
              toLogin()
            } else if (res.cancel) {
              console.log('用户点击取消')
            }
          }
        })
        return
      }
      uni.navigateTo({ url })
    },
    goTutorial(url) {
      uni.navigateTo({ url })
    },
    handleOrderSwitch() {
      if (!this.isLogin) {
        uni.showModal({
          title: '系统提示',
          content: '请先登录',
          success: function (res) {
            if (res.confirm) {
              toLogin()
            } else if (res.cancel) {
              console.log('用户点击取消')
            }
          }
        })
        return
      }

      const newValue = this.userInfo.worker_info.worker_open === 0 ? 1 : 0
      setWorkerOpen(newValue)
        .then((res) => {
          this.$util.Tips({ title: res.msg })
          this.getUserInfo()
        })
        .catch((err) => {
          this.$util.Tips({ title: err })
        })
    }
  },
  //#ifdef MP
  onShareAppMessage() {
    return {
      title: '欢迎加入好帮手师傅端',
      path: '/pages/tabs/index/index'
    }
  },
  //分享到朋友圈
  onShareTimeline: function () {
    return {
      title: '欢迎加入好帮手师傅端',
      path: '/pages/tabs/index/index'
    }
  }
  //#endif
}
</script>

<style lang="scss" scoped>
.user-center {
  min-height: 100vh;
  background-color: #f5f5f5;

  .head {
    // background: #fff;

    .user-card {
      position: relative;
      width: 100%;
      height: 380rpx;
      margin: 0 auto;
      padding: 35rpx 28rpx;
      background-image: url('~@/static/images/user01.png');
      background-size: 100% auto;
      background-color: var(--view-theme);

      .user-info {
        z-index: 20;
        position: relative;
        display: flex;

        .headwear {
          position: absolute;
          right: -4rpx;
          top: -14rpx;
          width: 44rpx;
          height: 44rpx;

          image {
            width: 100%;
            height: 100%;
          }
        }

        .live {
          width: 28rpx;
          height: 28rpx;
          margin-left: 20rpx;
        }

        .bntImg {
          width: 120rpx;
          height: 120rpx;
          border-radius: 50%;
          text-align: center;
          line-height: 120rpx;
          background-color: unset;
          position: relative;

          .avatarName {
            font-size: 16rpx;
            color: #fff;
            text-align: center;
            background-color: rgba(0, 0, 0, 0.6);
            height: 37rpx;
            line-height: 37rpx;
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
          }
        }

        .avatar-box {
          position: relative;
          display: flex;
          align-items: center;
          justify-content: center;
          width: 120rpx;
          height: 120rpx;
          border-radius: 50%;

          &.on {
            .avatar {
              border: 2px solid #ffac65;
              border-radius: 50%;
              box-sizing: border-box;
            }
          }
        }

        .avatar {
          position: relative;
          width: 120rpx;
          height: 120rpx;
          border-radius: 50%;
        }

        .info {
          flex: 1;
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          margin-left: 20rpx;
          padding: 20rpx 0;

          .order-switch {
            margin-top: 20rpx;

            .switch-content {
              display: flex;
              align-items: center;
              justify-content: space-between;
              padding: 20rpx 24rpx;
              background: rgba(255, 255, 255, 0.1);
              border-radius: 16rpx;
              backdrop-filter: blur(10px);

              .switch-left {
                display: flex;
                flex-direction: column;

                .switch-title {
                  font-size: 24rpx;
                  color: rgba(255, 255, 255, 0.9);
                  margin-bottom: 4rpx;
                }

                .switch-status {
                  font-size: 28rpx;
                  color: #ff4d4f;
                  font-weight: 500;

                  &.status-on {
                    color: #52c41a;
                  }
                }
              }

              .switch-right {
                .switch {
                  position: relative;
                  width: 92rpx;
                  height: 48rpx;
                  background: rgba(255, 255, 255, 0.2);
                  border-radius: 24rpx;
                  transition: all 0.3s ease;
                  padding: 4rpx;

                  &.active {
                    background: #52c41a;

                    .switch-handle {
                      transform: translateX(44rpx);

                      &::after {
                        background: #52c41a;
                      }
                    }
                  }

                  .switch-handle {
                    position: relative;
                    width: 40rpx;
                    height: 40rpx;
                    background: #fff;
                    border-radius: 50%;
                    transition: all 0.3s ease;
                    box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);

                    &::after {
                      content: '';
                      position: absolute;
                      top: 50%;
                      left: 50%;
                      transform: translate(-50%, -50%);
                      width: 16rpx;
                      height: 16rpx;
                      background: #ff4d4f;
                      border-radius: 50%;
                      transition: all 0.3s ease;
                    }
                  }
                }
              }
            }
          }

          .name {
            display: flex;
            align-items: center;
            color: #fff;
            font-size: 31rpx;

            .nickname {
              max-width: 8em;
            }

            .vip {
              margin-left: 10rpx;

              image {
                width: 78rpx;
                height: 30rpx;
                display: block;
              }
            }
          }

          .num {
            display: flex;
            align-items: center;
            font-size: 26rpx;
            color: rgba(255, 255, 255, 0.6);

            image {
              width: 22rpx;
              height: 23rpx;
              margin-left: 20rpx;
            }
          }
        }
      }

      .message {
        align-self: flex-start;
        position: relative;
        margin-top: 15rpx;
        margin-right: 20rpx;

        .num {
          position: absolute;
          top: -8rpx;
          left: 18rpx;
          padding: 0 6rpx;
          height: 28rpx;
          border-radius: 12rpx;
          background-color: #fff;
          font-size: 18rpx;
          line-height: 28rpx;
          text-align: center;
          color: var(--view-theme);
        }

        .iconfont {
          font-size: 40rpx;
          color: #fff;
        }
      }

      .num-wrapper {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-top: 50rpx;
        color: #fff;

        .num-item {
          width: 33.33%;
          text-align: center;

          & ~ .num-item {
            position: relative;

            &:before {
              content: '';
              position: absolute;
              width: 1rpx;
              height: 28rpx;
              top: 50%;
              margin-top: -14rpx;
              background-color: rgba(255, 255, 255, 0.4);
              left: 0;
            }
          }

          .num {
            font-size: 42rpx;
            font-weight: bold;
          }

          .txt {
            margin-top: 8rpx;
            font-size: 26rpx;
            color: rgba(255, 255, 255, 0.6);
          }
        }
      }
    }
  }

  .body {
    padding: 28rpx;
    position: relative;
    z-index: 9;
    margin-top: -80rpx;

    .switch-block {
      margin-bottom: 20rpx;

      .switch-card {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 32rpx;
        background: #ffffff;
        border-radius: 16rpx;
        box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.03);

        .switch-info {
          display: flex;
          align-items: center;
          gap: 20rpx;

          .switch-icon {
            width: 80rpx;
            height: 80rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f6f7f9;
            border-radius: 20rpx;

            .iconfont {
              font-size: 40rpx;
              color: var(--view-theme);
            }
          }

          .switch-text {
            display: flex;
            flex-direction: column;
            gap: 8rpx;

            .switch-title {
              font-size: 32rpx;
              color: #333;
              font-weight: 500;
            }

            .switch-desc {
              font-size: 26rpx;
              color: #ff4d4f;

              &.status-on {
                color: #52c41a;
              }
            }
          }
        }

        .switch-control {
          .switch {
            position: relative;
            width: 92rpx;
            height: 48rpx;
            background: #f5f5f5;
            border-radius: 24rpx;
            transition: all 0.3s ease;
            padding: 4rpx;

            &.active {
              background: #52c41a;

              .switch-handle {
                transform: translateX(44rpx);

                &::after {
                  background: #52c41a;
                }
              }
            }

            .switch-handle {
              position: relative;
              width: 40rpx;
              height: 40rpx;
              background: #fff;
              border-radius: 50%;
              transition: all 0.3s ease;
              box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);

              &::after {
                content: '';
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                width: 16rpx;
                height: 16rpx;
                background: #ff4d4f;
                border-radius: 50%;
                transition: all 0.3s ease;
              }
            }
          }
        }
      }
    }

    .function-list {
      background-color: #fff;
      border-radius: 16rpx;

      .function-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 30rpx;
        border-bottom: 1rpx solid #eee;

        &:last-child {
          border-bottom: none;
        }

        .function-left {
          display: flex;
          align-items: center;
          flex: 1;
        }

        .function-right {
          display: flex;
          align-items: center;
          gap: 20rpx;

          .tutorial-tip {
            font-size: 24rpx;
            color: #ff4d4f;
            padding: 4rpx 12rpx;
            border: 1px solid #ff4d4f;
            border-radius: 8rpx;
          }

          .iconfont {
            font-size: 28rpx;
            color: #999;
          }
        }

        .function-icon {
          width: 48rpx;
          height: 48rpx;
          margin-right: 20rpx;
        }

        .function-name {
          font-size: 30rpx;
          color: #333;
          flex-shrink: 0;
        }

        .function-status {
          font-size: 24rpx;
          color: #999;
          margin-left: 20rpx;
          background-color: #f3f3f5;
          padding: 8rpx 10rpx;
          border-radius: 8rpx;
        }
      }
    }
  }
}

.master-popup {
  width: 600rpx;
  background: linear-gradient(to bottom, #ffffff, #f8f9fa);
  border-radius: 24rpx;
  overflow: hidden;

  .popup-header {
    padding: 40rpx;
    text-align: center;
    position: relative;

    .popup-icon {
      width: 120rpx;
      height: 120rpx;
      margin-bottom: 24rpx;
    }

    .popup-title {
      font-size: 36rpx;
      font-weight: 600;
      color: #333;
      margin-bottom: 16rpx;
      display: block;
    }

    .popup-subtitle {
      font-size: 28rpx;
      color: #666;
      display: block;
    }
  }

  .popup-body {
    padding: 0 40rpx 40rpx;

    .benefit-list {
      display: flex;
      justify-content: space-around;

      .benefit-item {
        display: flex;
        flex-direction: column;
        align-items: center;

        .iconfont {
          font-size: 48rpx;
          color: var(--view-theme);
          margin-bottom: 12rpx;
        }

        .benefit-text {
          font-size: 26rpx;
          color: #666;
        }
      }
    }
  }

  .popup-footer {
    display: flex;
    padding: 30rpx 40rpx;
    gap: 24rpx;
    border-top: 1rpx solid #ebeef5;

    .popup-btn {
      flex: 1;
      height: 80rpx;
      border-radius: 40rpx;
      font-size: 28rpx;
      font-weight: 500;
      display: flex;
      align-items: center;
      justify-content: center;

      &.cancel {
        background: #f5f7fa;
        color: #666;

        &:active {
          background: #ebeef5;
        }
      }

      &.confirm {
        background: var(--view-theme);
        color: #fff;

        &:active {
          opacity: 0.9;
        }
      }
    }
  }
}

.mark {
  font-size: 26rpx;
  text-align: center;
  color: #7a7a7a;

  .logOut {
    font-size: 32rpx;
    text-align: center;
    width: 690rpx;
    height: 90rpx;
    border-radius: 15rpx;
    margin: 30rpx auto 30rpx auto;
    color: var(--view-theme);
    border: 1px solid var(--view-theme);
  }
}
</style>
