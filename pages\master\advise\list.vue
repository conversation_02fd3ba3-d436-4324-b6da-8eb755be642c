<template>
  <view>
    <view class="container" :style="colorStyle">
      <scroll-view
        scroll-y
        class="advise-list"
        :refresher-enabled="true"
        :refresher-triggered="isRefreshing"
        @refresherrefresh="onRefresh"
        @scrolltolower="onReachBottom"
      >
        <view class="list" v-if="recordList.length">
          <block v-for="(item, index) in recordList" :key="index">
            <view class="item" @click="showReply(item)">
              <view class="header">
                <view class="title">{{ item.name }}</view>
                <view class="view-reply">
                  {{ $t('查看详情') }}
                  <text class="iconfont icon-jiantou"></text>
                </view>
              </view>
              <view class="content">{{ item.content }}</view>

              <view class="footer">
                <view class="time">{{ item.add_time }}</view>
              </view>
            </view>
          </block>
        </view>

        <view class="empty-state" v-else>
          <view class="empty-text">{{ $t('暂无反馈记录') }}</view>
        </view>
        <view class="loading-more" v-if="recordList.length">
          <text class="loading iconfont icon-jiazai" v-if="loading"></text>
          <text>{{ loadTitle }}</text>
        </view>
      </scroll-view>
      <!-- 添加提交意见按钮 -->
      <button hover-class="button-hover" class="submit-btn" @click="goSubmit">
        <text class="btn-text">{{ $t('提交意见') }}</text>
      </button>
    </view>
  </view>
</template>

<script>
import colors from '@/mixins/color.js'
import { adviseList } from '@/api/customer.js'

export default {
  mixins: [colors],
  data() {
    return {
      page: 1,
      limit: 10,
      recordList: [],
      loading: false,
      loadend: false,
      loadTitle: this.$t('加载更多'),
      isRefreshing: false
    }
  },

  onLoad() {
    this.getAdviseList()
  },

  methods: {
    async onRefresh() {
      this.isRefreshing = true
      this.page = 1
      this.loadend = false
      this.recordList = []
      await this.getAdviseList()
      this.isRefreshing = false
    },

    async getAdviseList() {
      if (this.loading || this.loadend) return
      this.loading = true
      this.loadTitle = this.$t('加载中...')

      try {
        const res = await adviseList({
          page: this.page,
          limit: this.limit
        })

        const list = res.data.list
        const loadend = list.length < this.limit

        if (this.page === 1) {
          this.recordList = list
        } else {
          this.recordList = [...this.recordList, ...list]
        }

        this.loadend = loadend
        this.loadTitle = loadend ? this.$t('没有更多内容啦~') : this.$t('加载更多')
        this.page++
      } catch (error) {
        this.loadTitle = this.$t('加载失败,请重试')
        uni.showToast({
          title: this.$t('加载失败'),
          icon: 'none'
        })
      } finally {
        this.loading = false
      }
    },

    previewImage(urls, current) {
      uni.previewImage({
        urls,
        current: urls[current],
        loop: true,
        indicator: 'number'
      })
    },

    showReply(item) {
      // 去详情页
      uni.navigateTo({ url: `/pages/master/advise/detail?id=${item.id}` })
    },

    goSubmit() {
      uni.navigateTo({ url: '/pages/master/advise/advise' })
    }
  }
}
</script>

<style lang="scss" scoped>
.advise-list {
  height: 100vh;
  padding: 20rpx;
  box-sizing: border-box;

  .list {
    .item {
      background: #fff;
      border-radius: 12rpx;
      padding: 30rpx;
      margin-bottom: 20rpx;
      box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
      transition: all 0.3s ease;

      .header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 20rpx;

        .title {
          font-size: 32rpx;
          font-weight: bold;
          color: #333;
        }
        .view-reply {
          display: flex;
          align-items: center;
          font-size: 26rpx;
          color: #b2b2b2;
          padding: 8rpx 16rpx;
          border-radius: 24rpx;

          .iconfont {
            font-size: 24rpx;
            margin-left: 4rpx;
          }
        }
      }

      .content {
        font-size: 28rpx;
        color: #666;
        line-height: 1.6;
        margin-bottom: 20rpx;
      }

      .footer {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-top: 20rpx;

        .time {
          font-size: 24rpx;
          color: #999;
        }
      }
    }
  }

  .empty-state {
    padding-top: 200rpx;
    text-align: center;

    .empty-text {
      font-size: 32rpx;
      color: #333;
      margin-bottom: 16rpx;
    }

    .empty-tip {
      font-size: 26rpx;
      color: #999;
    }
  }

  .loading-more {
    text-align: center;
    padding: 30rpx 0;
    color: #999;
    font-size: 24rpx;

    .loading {
      display: inline-block;
      animation: rotate 1s linear infinite;
      margin-right: 10rpx;
    }
  }
}

.submit-btn {
  position: fixed;
  left: 30rpx;
  right: 30rpx;
  bottom: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 88rpx;
  border-radius: 44rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
  z-index: 100;
  background-color: var(--view-theme);

  .btn-text {
    font-size: 32rpx;
    color: #fff;
    font-weight: 500;
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
