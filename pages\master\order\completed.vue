<template>
  <view class="container" :style="colorStyle">
    <!-- 订单信息卡片 -->
    <view class="card order-info">
      <view class="card-title">订单信息</view>
      <view class="info-list">
        <view class="info-item">
          <text class="label">订单编号</text>
          <view class="value">{{ orderInfo.order_no }}</view>
        </view>
        <view class="info-item">
          <text class="label">服务地址</text>
          <view class="value">
            {{ orderInfo.province_name }}/{{ orderInfo.city_name }}/{{ orderInfo.district_name }}
            {{ orderInfo.user_address }}
          </view>
        </view>
      </view>
    </view>

    <!-- 图片上传卡片 -->
    <view class="card upload-section">
      <view class="card-title">
        <text>完工图片</text>
        <text class="subtitle">（至少上传3张图片）</text>
      </view>
      <view class="image-list">
        <view class="image-item" v-for="(item, index) in attachmentImage" :key="index">
          <image :src="item" mode="aspectFill" @tap="previewImage(index)"></image>
          <view class="delete-btn" @tap.stop="deleteImage(index)">
            <text class="iconfont icon-guanbi"></text>
          </view>
        </view>
        <view class="upload-btn" @tap="chooseImage" v-if="attachmentImage.length < 5">
          <text class="iconfont icon-xiangji"></text>
          <text class="tip">上传图片</text>
        </view>
      </view>
      <text class="tips" v-if="attachmentImage.length < 3">请至少上传3张图片，建议上传施工现场照片</text>
    </view>
    <view class="card upload-section">
      <view class="card-title">
        <text>完工视频</text>
        <text class="subtitle">（请上传完工视频）</text>
      </view>
      <view class="video-list">
        <view class="video-item" v-for="(item, index) in attachmentVideo" :key="index">
          <video :src="item" class="video"></video>
          <view class="video-actions">
            <button hover-class="button-hover" class="action-btn" @click="chooseVideo">重新录制</button>
            <button hover-class="button-hover" class="action-btn delete" @click="deleteVideo(index)">删除</button>
          </view>
        </view>
        <view class="upload-btn" @tap="chooseVideo" v-if="attachmentVideo.length < 1">
          <text class="iconfont icon-xiangji"></text>
          <text class="tip">上传视频</text>
        </view>
      </view>
      <text class="tips" v-if="attachmentVideo.length < 1">请至少上传1个施工现场视频</text>
    </view>
    <!-- 提交按钮 -->
    <view class="bottom-btn-wrap">
      <button class="submit-btn" :disabled="attachmentImage.length < 3" @click="submitStartWork" hover-class="button-hover">
        确认完工
      </button>
    </view>
  </view>
</template>

<script>
import colors from '@/mixins/color'
import { postOrderOver } from '@/api/worker'
export default {
  mixins: [colors],
  data() {
    return {
      orderInfo: {
        order_no: '',
        province_name: '',
        city_name: '',
        district_name: '',
        user_address: ''
      },
      attachmentImage: [],
      attachmentVideo: [],
      completed_confirm: 'no',
      completedvideo_confirm: 'no'
    }
  },
  onLoad(options) {
    if (options.orderInfo) {
      this.orderInfo = JSON.parse(options.orderInfo)
    }
  },
  methods: {
    chooseImage() {
      let that = this
      that.completed_confirm = uni.getStorageSync('completed_confirm')
      if (that.completed_confirm === 'yes') {
        this.$util.uploadImageChange('upload/image', res => {
          this.attachmentImage.push(res.data.url)
        })
        return true
      }
      uni.showModal({
        title: '系统提示',
        content:
          '确定使用您手机的摄像头和读取相册权限，上传完工图片吗？ 如果您已禁用(点击[同意上传]后无法上传)，请到手机设置授权本APP的摄像头和读取相册权限',
        showCancel: true,
        cancelColor: '#0087fc',
        confirmColor: '#fa1722',
        cancelText: '不同意',
        confirmText: '同意上传',
        success: res => {
          if (res.confirm) {
            console.log('用户点击确定')
            uni.setStorageSync('completed_confirm', 'yes')
            that.$util.uploadImageChange('upload/image', res => {
              that.attachmentImage.push(res.data.url)
            })
          } else if (res.cancel) {
            console.log('用户点击取消')
          }
        }
      })
    },
    chooseVideo() {
      let that = this
      that.completedvideo_confirm = uni.getStorageSync('completedvideo_confirm')
      if (that.completedvideo_confirm === 'yes') {
        this.$util.uploadVideoOne(
          {
            url: 'upload/image',
            count: 1,
            sourceType: ['camera']
          },
          res => {
            this.attachmentVideo.push(res.data.url)
          }
        )
        return true
      }
      uni.showModal({
        title: '系统提示',
        content:
          '确定使用您手机的摄像头和读取相册权限，上传完工视频吗？ 如果您已禁用(点击[同意上传]后无法上传)，请到手机设置授权本APP的摄像头和读取相册权限',
        showCancel: true,
        cancelColor: '#0087fc',
        confirmColor: '#fa1722',
        cancelText: '不同意',
        confirmText: '同意上传',
        success: res => {
          if (res.confirm) {
            console.log('用户点击确定')
            uni.setStorageSync('completedvideo_confirm', 'yes')
            that.$util.uploadVideoOne('upload/image', res => {
              that.attachmentVideo.push(res.data.url)
            })
          } else if (res.cancel) {
            console.log('用户点击取消')
          }
        }
      })
    },
    deleteImage(index) {
      uni.showModal({
        title: '提示',
        content: '确定要删除这张图片吗？',
        success: res => {
          if (res.confirm) {
            this.attachmentImage.splice(index, 1)
          }
        }
      })
    },
    deleteVideo(index) {
      uni.showModal({
        title: '提示',
        content: '确定要删除这个视频吗？',
        success: res => {
          if (res.confirm) {
            this.attachmentVideo.splice(index, 1)
          }
        }
      })
    },
    previewImage(index) {
      uni.previewImage({
        urls: this.attachmentImage,
        current: index
      })
    },
    async submitStartWork() {
      if (this.attachmentImage.length < 3) {
        this.$util.Tips({ title: '请至少上传3张图片' })
        return
      }
      if (this.attachmentVideo.length < 1) {
        this.$util.Tips({ title: '请至少上传1个施工视频' })
        return
      }

      try {
        uni.showLoading({ title: '提交中...' })
        // TODO: 调用提交接口
        postOrderOver(this.orderInfo.id, {
          over_image: this.attachmentImage.join(','),
          over_video: this.attachmentVideo[0]
        })
          .then(res => {
            this.$util.Tips({ title: res.msg })
            setTimeout(() => {
              uni.navigateBack()
            }, 1500)
          })
          .catch(err => {
            this.$util.Tips({ title: err || '提交失败，请重试' })
          })
      } catch (e) {
        this.$util.Tips({ title: '提交失败，请重试' })
      } finally {
        uni.hideLoading()
      }
    }
  }
}
</script>

<style lang="scss">
page {
  background: #f5f5f5;
  min-height: 100%;
}

.container {
  padding: 20rpx;
  padding-bottom: 140rpx;
}

.card {
  background: #fff;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;

  .card-title {
    font-size: 32rpx;
    font-weight: 500;
    color: #333;
    margin-bottom: 24rpx;

    .subtitle {
      font-size: 24rpx;
      color: #999;
      font-weight: normal;
      margin-left: 12rpx;
    }
  }
}

.order-info {
  .info-list {
    .info-item {
      display: flex;
      margin-bottom: 20rpx;
      align-items: flex-start;

      &:last-child {
        margin-bottom: 0;
      }

      .label {
        color: #666;
        width: 140rpx;
        font-size: 28rpx;
        flex-shrink: 0;
      }

      .value {
        flex: 1;
        color: #333;
        font-size: 28rpx;
      }
    }
  }
}

.upload-section {
  .image-list {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    column-gap: 16rpx;
    row-gap: 16rpx;

    .image-item,
    .upload-btn {
      width: 100%;
      height: 216rpx;
      position: relative;
      border-radius: 8rpx;
      overflow: hidden;
    }

    .image-item {
      image {
        width: 100%;
        height: 100%;
      }

      .video {
        width: 100%;
        height: 100%;
      }

      .delete-btn {
        position: absolute;
        right: 0;
        top: 0;
        width: 44rpx;
        height: 44rpx;
        background: rgba(0, 0, 0, 0.6);
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;

        .iconfont {
          color: #fff;
          font-size: 24rpx;
        }
      }
    }

    .upload-btn {
      background: #f7f7f7;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;

      .iconfont {
        font-size: 56rpx;
        color: #999;
        margin-bottom: 12rpx;
      }

      .tip {
        font-size: 26rpx;
        color: #999;
      }
    }
  }

  .tips {
    color: var(--view-theme);
    font-size: 24rpx;
    margin-top: 20rpx;
    display: block;
  }
}

.bottom-btn-wrap {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  background: #fff;
  padding: 20rpx 40rpx;
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
  box-shadow: 0 -2rpx 8rpx rgba(0, 0, 0, 0.05);

  .submit-btn {
    height: 88rpx;
    line-height: 88rpx;
    font-size: 32rpx;
    border-radius: 44rpx;
    background: var(--view-theme);
    color: #fff;

    &:disabled {
      background: #ccc !important;
    }
  }
}
.video-list {
  width: 100%;
  .upload-btn {
    width: 100%;
    height: 360rpx;
    position: relative;
    border-radius: 8rpx;
    background: #f7f7f7;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    .iconfont {
      font-size: 56rpx;
      color: #999;
      margin-bottom: 12rpx;
    }
    .tip {
      font-size: 26rpx;
      color: #999;
    }
  }

  .video {
    width: 100%;
    height: 360rpx;
    border-radius: 8rpx 8rpx 0 0;
    vertical-align: bottom;
  }
  .video-actions {
    display: flex;
    padding: 24rpx;
    background: rgba(0, 0, 0, 0.75);
    backdrop-filter: blur(10px);
    gap: 24rpx;
    border-radius: 0 0 8rpx 8rpx;
    .action-btn {
      flex: 1;
      height: 76rpx;
      border-radius: 38rpx;
      font-size: 28rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.2s ease;
      font-weight: 500;

      &:first-child {
        background: rgba(255, 255, 255, 0.2);
        color: #fff;
        border: 1px solid rgba(255, 255, 255, 0.3);
      }

      &.delete {
        background: rgba(244, 81, 30, 0.9);
        color: #fff;
      }
    }
  }
}
</style>
