<template>
  <view class="check-in-page" :style="colorStyle">
    <!-- 签到功能区 -->
    <view class="check-in-actions">
      <button class="action-btn check-in" @click="handleCheckIn('work_start')" :disabled="todayCheckedIn">
        <text class="iconfont icon-signin"></text>
        <text>上门签到</text>
      </button>
      <button class="action-btn check-out" @click="handleCheckIn('work_end')" :disabled="!todayCheckedIn || todayCheckedOut">
        <!-- <button class="action-btn check-out" @click="handleCheckIn('work_end')"> -->
        <text class="iconfont icon-signout"></text>
        <text>下班签到</text>
      </button>
    </view>

    <!-- 签到列表 -->
    <view class="check-in-list">
      <view class="list-header">
        <text class="title">签到记录</text>
      </view>
      <view class="list-content">
        <view v-for="(item, index) in checkInList" :key="index" class="check-in-item" @click="goToDetail(item)">
          <view class="date-info">
            <text class="date">{{ item.add_time }}</text>
            <text class="type">{{ item.type_txt }}</text>
          </view>
          <view class="check-info">
            <view class="info-row">
              <text class="label">签到人：</text>
              <text class="value">{{ item.workerInfo.real_name }}</text>
            </view>
            <view class="info-row" v-if="item.type === 'work_start'">
              <text class="label">上门人数：</text>
              <text class="value">{{ item.start_num }}人</text>
            </view>
            <view class="info-row" v-if="item.type === 'work_end'">
              <text class="label">下班人数：</text>
              <text class="value">{{ item.end_num }}人</text>
            </view>
            <!-- <view class="image-preview" v-if="item.type === 'work_start' && item.start_img">
              <image :src="item.start_img" mode="aspectFill" @click="previewImage(item.start_img)"></image>
            </view> -->
          </view>
        </view>
        <view v-if="checkInList.length === 0" class="empty-tip">暂无签到记录</view>
      </view>
    </view>
  </view>
</template>

<script>
import colors from '@/mixins/color'
import { getOrderSignList } from '@/api/worker'

export default {
  mixins: [colors],
  data() {
    return {
      orderId: '',
      checkInList: [],
      todayCheckedIn: false,
      todayCheckedOut: false
    }
  },

  onLoad(options) {
    this.orderId = options.orderId
    this.loadCheckInList()
  },

  methods: {
    // 加载签到列表
    async loadCheckInList() {
      try {
        const res = await getOrderSignList(this.orderId)
        if (res.status === 200) {
          this.checkInList = res.data.list
          // 检查今天是否已签到/签退
          const today = new Date().toISOString().split('T')[0]
          const todayRecords = this.checkInList.filter((item) => item.add_time.startsWith(today))
          this.todayCheckedIn = todayRecords.some((item) => item.type === 'work_start')
          this.todayCheckedOut = todayRecords.some((item) => item.type === 'work_end')
        }
      } catch (error) {
        this.$util.Tips({ title: '获取签到记录失败' })
      }
    },

    // 处理签到
    handleCheckIn(type) {
      const url = type === 'work_start' ? 'work_start_sign' : 'work_end_sign'
      uni.navigateTo({
        url: `/pages/master/order/${url}?orderId=${this.orderId}`
      })
    },

    // 预览图片
    previewImage(url) {
      uni.previewImage({
        urls: [url]
      })
    },
    goToDetail(item) {
      uni.navigateTo({
        url: `/pages/master/order/check_in_detail?id=${item.id}&result=${encodeURIComponent(JSON.stringify(item))}`
      })
    }
  }
}
</script>

<style lang="scss">
.check-in-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 20rpx;

  .check-in-actions {
    display: flex;
    justify-content: space-between;
    padding: 20rpx;
    background-color: #fff;
    border-radius: 12rpx;
    margin-bottom: 20rpx;

    .action-btn {
      flex: 1;
      margin: 0 10rpx;
      height: 88rpx;
      border-radius: 44rpx;
      font-size: 32rpx;
      display: flex;
      align-items: center;
      justify-content: center;

      .iconfont {
        margin-right: 8rpx;
        font-size: 36rpx;
      }

      &.check-in {
        background: var(--view-theme);
        color: #fff;

        &[disabled] {
          background: #ccc;
        }
      }

      &.check-out {
        background: #fff;
        color: var(--view-theme);
        border: 2rpx solid var(--view-theme);

        &[disabled] {
          color: #999;
          border-color: #ddd;
        }
      }
    }
  }

  .check-in-list {
    background-color: #fff;
    border-radius: 12rpx;
    overflow: hidden;

    .list-header {
      padding: 30rpx;
      border-bottom: 1rpx solid #f5f5f5;

      .title {
        font-size: 32rpx;
        font-weight: bold;
        color: #333;
      }
    }

    .list-content {
      .check-in-item {
        padding: 30rpx;
        border-bottom: 1rpx solid #f5f5f5;

        .date-info {
          display: flex;
          justify-content: space-between;
          margin-bottom: 20rpx;

          .date {
            font-size: 28rpx;
            color: #333;
          }

          .type {
            font-size: 26rpx;
            color: var(--view-theme);
          }
        }

        .check-info {
          .info-row {
            display: flex;
            margin-bottom: 10rpx;

            .label {
              width: 140rpx;
              color: #666;
              font-size: 26rpx;
            }

            .value {
              flex: 1;
              color: #333;
              font-size: 26rpx;
            }
          }

          .image-preview {
            margin-top: 20rpx;
            width: 200rpx;
            height: 200rpx;
            border-radius: 8rpx;
            overflow: hidden;

            image {
              width: 100%;
              height: 100%;
              object-fit: cover;
            }
          }
        }
      }

      .empty-tip {
        padding: 60rpx 0;
        text-align: center;
        color: #999;
        font-size: 28rpx;
      }
    }
  }
}
</style>
