<template>
  <page-meta :page-style="'overflow:' + (categoryPopupVisible ? 'hidden' : 'visible')"></page-meta>
  <view class="enterprise-container" :style="colorStyle">
    <form @submit="formSubmit">
      <!-- 头部标题 -->
      <view class="header">
        <view class="title">{{ $t(`认证信息`) }}</view>
        <view class="subtitle">
          <text>{{ $t(`完善认证信息，享受更多服务`) }}</text>
          <view class="status-tag" :class="statusClass">{{ statusText }}</view>
        </view>
      </view>
      <view class="form-content">
        <!-- 联系方式 -->
        <view class="form-section">
          <view class="section-title">{{ $t(`联系方式`) }}</view>
          <!-- 身份证号 -->
          <view class="form-item">
            <view class="label">
              {{ $t(`身份证号`) }}
              <text class="required">*</text>
            </view>
            <view class="content">
              <input class="input" type="idcard" name="card_id" :value="userInfo.card_id" :placeholder="$t(`请输入身份证号`)" />
            </view>
          </view>

          <!-- 真实姓名 -->
          <view class="form-item">
            <view class="label">
              {{ $t(`真实姓名`) }}
              <text class="required">*</text>
            </view>
            <view class="content">
              <input class="input" type="text" name="real_name" :value="userInfo.real_name" :placeholder="$t(`请输入真实姓名`)" />
            </view>
          </view>
          <!-- 紧急联系人姓名 -->
          <view class="form-item">
            <view class="label">
              {{ $t(`紧急联系人姓名`) }}
              <text class="required">*</text>
            </view>
            <view class="content">
              <input
                class="input"
                type="text"
                name="contact_name"
                :value="userInfo.contact_name"
                :placeholder="$t(`请输入紧急联系人姓名`)"
              />
            </view>
          </view>
          <!-- 紧急联系人关系 -->
          <view class="form-item">
            <view class="label">
              {{ $t(`紧急联系人关系`) }}
              <text class="required">*</text>
            </view>
            <view class="content">
              <input
                class="input"
                type="text"
                name="contact_type"
                :value="userInfo.contact_type"
                :placeholder="$t(`请输入与紧急联系人关系`)"
              />
            </view>
          </view>
          <!-- 紧急联系人电话 -->
          <view class="form-item">
            <view class="label">
              {{ $t(`紧急联系人电话`) }}
              <text class="required">*</text>
            </view>
            <view class="content">
              <input
                class="input"
                type="number"
                name="contact_phone"
                :value="userInfo.contact_phone"
                :placeholder="$t(`请输入紧急联系人电话`)"
              />
            </view>
          </view>
        </view>

        <!-- 地址信息 -->
        <view class="form-section">
          <view class="section-title">{{ $t(`地址信息`) }}</view>

          <!-- 所在地区 -->
          <view class="form-item">
            <view class="label">
              {{ $t(`所在地区`) }}
              <text class="required">*</text>
            </view>
            <view class="content">
              <picker
                mode="multiSelector"
                @change="bindRegionChange"
                @columnchange="bindMultiPickerColumnChange"
                :value="valueRegion"
                :range="multiArray"
              >
                <view class="picker-content">
                  <text>{{ region[0] == '省' ? $t('请选择所在地区') : region[0] + '/' + region[1] + '/' + region[2] }}</text>
                  <text class="iconfont icon-arrow-right"></text>
                </view>
              </picker>
            </view>
          </view>
        </view>
        <!-- 新增工作经验部分 -->
        <view class="form-section">
          <view class="section-title">{{ $t(`工作经验`) }}</view>

          <!-- 服务技能 -->
          <view class="form-item">
            <view class="label">
              {{ $t(`服务技能`) }}
              <text class="required">*</text>
            </view>
            <view class="content">
              <view class="picker-content" @click="showCategorySelector">
                <text>{{ selectedCategories || $t('请选择服务技能') }}</text>
                <text class="iconfont icon-arrow-right"></text>
              </view>
            </view>
          </view>
          <uni-popup ref="popup" type="bottom" background-color="#fff" @change="change">
            <!-- 经营品类选择弹窗 -->
            <view class="category-popup">
              <view class="category-popup-content">
                <view class="category-popup-header">
                  <view class="category-popup-title">{{ $t(`请选择服务技能`) }}</view>
                  <view class="category-popup-close" @click="hideCategorySelector">
                    <text class="iconfont icon-guanbi"></text>
                  </view>
                </view>
                <view class="category-popup-body">
                  <checkbox-group @change="categoryCheckboxChange">
                    <!-- 一级分类 -->
                    <view v-for="(category, index) in businessCategoryList" :key="index">
                      <view class="category-item level-1">
                        <checkbox :value="category.id" :checked="selectedCategoryIds.includes(category.id)" />
                        <text class="category-name">{{ category.name }}</text>
                      </view>

                      <!-- 二级分类 -->
                      <view v-if="category.children && category.children.length > 0" class="sub-categories">
                        <view v-for="(subCategory, subIndex) in category.children" :key="subIndex" class="category-item level-2">
                          <checkbox :value="subCategory.id" :checked="selectedCategoryIds.includes(subCategory.id)" />
                          <text class="category-name">{{ subCategory.name }}</text>

                          <!-- 三级分类 -->
                          <view v-if="subCategory.children && subCategory.children.length > 0" class="sub-sub-categories">
                            <view
                              v-for="(subSubCategory, subSubIndex) in subCategory.children"
                              :key="subSubIndex"
                              class="category-item level-3"
                            >
                              <checkbox :value="subSubCategory.id" :checked="selectedCategoryIds.includes(subSubCategory.id)" />
                              <text class="category-name">{{ subSubCategory.name }}</text>
                            </view>
                          </view>
                        </view>
                      </view>
                    </view>
                  </checkbox-group>
                </view>
                <view class="category-popup-footer">
                  <view class="category-popup-btn cancel" @click="hideCategorySelector">{{ $t(`取消`) }}</view>
                  <view class="category-popup-btn confirm" @click="confirmCategorySelection">{{ $t(`确定`) }}</view>
                </view>
              </view>
            </view>
          </uni-popup>

          <!-- 工作年限 -->
          <view class="form-item">
            <view class="label">
              {{ $t(`工作年限`) }}
              <text class="required">*</text>
            </view>
            <view class="content">
              <picker mode="selector" :range="yearRange" @change="handleYearChange" :value="yearIndex">
                <view class="picker-content">
                  <text>{{ userInfo.experience_year ? userInfo.experience_year + '年' : $t('请选择工作年限') }}</text>
                  <text class="iconfont icon-arrow-right"></text>
                </view>
              </picker>
            </view>
          </view>

          <!-- 个人介绍 -->
          <view class="form-item vertical-wrap">
            <view class="label">{{ $t(`个人介绍`) }}</view>
            <view class="content">
              <textarea class="textarea" name="introduction" :value="userInfo.introduction" :placeholder="$t(`请输入个人介绍`)" />
            </view>
          </view>
        </view>
      </view>
      <!-- 提交按钮 -->
      <view class="submit-btn-wrapper">
        <button class="submit-btn" form-type="submit">{{ $t(`提交`) }}</button>
      </view>
    </form>
    <!-- 裁剪图片 -->
    <canvas
      v-if="canvasStatus"
      canvas-id="canvas"
      :style="{
        width: canvasWidth + 'px',
        height: canvasHeight + 'px',
        position: 'fixed',
        left: '-1000000px',
        top: '-1000000px'
      }"
    ></canvas>
  </view>
</template>

<script>
import { getCityListOpen, updateEnterpriseInfo, getWorkerUserInfo, getSkillTree } from '@/api/worker.js'
import { mapGetters } from 'vuex'
import colors from '@/mixins/color.js'

export default {
  mixins: [colors],
  data() {
    return {
      uid: 0,
      userInfo: {
        card_id: '',
        real_name: '',
        contact_type: '',
        contact_name: '',
        contact_phone: '',
        province: '',
        city: '',
        district: '',
        experience_skill: '',
        experience_year: '',
        introduction: '',
        is_worker: 0 // 认证状态: 0未认证 1已认证 2待审核
      },
      // 头像上传相关
      mp_is_new: false,
      canvasStatus: false,
      canvasWidth: 300,
      canvasHeight: 300,
      // 经营品类
      businessCategoryList: [],
      businessCategoryListBackups: [],
      businessCategoryIndex: [0],
      businessCategory: '',
      selectedCategories: '',
      categoryPopupVisible: false,
      selectedCategoryIds: [],
      tempSelectedCategoryIds: [],
      // 地区选择
      region: ['省', '市', '区'],
      valueRegion: [0, 0, 0],
      district: [],
      multiArray: [],
      multiIndex: [0, 0, 0],
      cityId: 0,
      provinceId: 0,
      districtId: 0,
      yearRange: ['1年', '2年', '3年', '4年', '5年', '6年', '7年', '8年', '9年', '10年'],
      yearIndex: 0,
      is_worker: 0
    }
  },
  computed: {
    ...mapGetters(['isLogin']),
    statusClass() {
      const statusMap = {
        0: 'status-pending',
        1: 'status-certified',
        2: 'status-reviewing'
      }
      return statusMap[this.is_worker] || 'status-pending'
    },
    statusText() {
      const statusMap = {
        0: this.$t('未认证'),
        1: this.$t('已认证'),
        2: this.$t('审核中')
      }
      return statusMap[this.is_worker] || this.$t('未认证')
    }
  },
  onLoad() {
    // 获取用户信息
    if (this.isLogin) {
      this.getUserInfo()
      this.getCityList()
      this.loadBusinessCategories()
    }

    // 判断是否为新版微信小程序
    // #ifdef MP-WEIXIN
    if (wx.canIUse('chooseAvatar')) {
      this.mp_is_new = true
    }
    // #endif
  },
  methods: {
    // 获取用户信息
    getUserInfo() {
      getWorkerUserInfo().then((res) => {
        if (res.data) {
          // 设置地区
          if (res.data.worker_info.province_name && res.data.worker_info.city_name && res.data.worker_info.district_name) {
            this.region = [res.data.worker_info.province_name, res.data.worker_info.city_name, res.data.worker_info.district_name]
          }
          this.provinceId = res.data.worker_info.province
          this.cityId = res.data.worker_info.city
          this.districtId = res.data.worker_info.district
          this.uid = res.data.worker_info.uid

          this.userInfo.card_id = res.data.worker_info.card_id
          this.userInfo.real_name = res.data.worker_info.real_name
          this.userInfo.contact_type = res.data.worker_info.contact_type
          this.userInfo.contact_name = res.data.worker_info.contact_name
          this.userInfo.contact_phone = res.data.worker_info.contact_phone

          this.userInfo.experience_skill = res.data.worker_info.experience_skill
          this.userInfo.experience_year = res.data.worker_info.experience_year
          this.userInfo.introduction = res.data.worker_info.introduction
          this.userInfo.province = res.data.worker_info.province
          this.userInfo.city = res.data.worker_info.city
          this.userInfo.district = res.data.worker_info.district
          // 服务技能文字
          if (res.data.worker_info.experience_skill) {
            this.selectedCategories = res.data.worker_info.experience_skill_name.join(',')
            this.selectedCategoryIds = res.data.worker_info.experience_skill
          }
          this.is_worker = res.data.worker_info.is_worker
          console.log('🚀 ~ getWorkerUserInfo ~ res.data.worker_info.is_worker:', res.data.worker_info.is_worker)

          if (res.data.worker_info.experience_year) {
            this.yearIndex = res.data.worker_info.experience_year - 1
          }
        }
      })
    },

    // 显示经营品类选择器
    showCategorySelector() {
      try {
        console.log('🚀 ~ showCategorySelector ~  [...this.selectedCategoryIds]:', this.selectedCategoryIds)
        this.tempSelectedCategoryIds = [...this.selectedCategoryIds]
        this.$refs.popup.open()
      } catch (error) {
        //TODO handle the exception
        console.log('🚀 ~ showCategorySelector ~ error:', error)
      }
    },
    change(e) {
      this.categoryPopupVisible = e.show
    },
    // 隐藏经营品类选择器
    hideCategorySelector() {
      this.$refs.popup.close()
    },

    // 经营品类多选框变化
    categoryCheckboxChange(e) {
      console.log('🚀 ~ categoryCheckboxChange ~ e:', e)
      this.tempSelectedCategoryIds = e.detail.value
    },

    // 确认经营品类选择
    confirmCategorySelection() {
      this.selectedCategories = this.tempSelectedCategoryIds
        .map((categoryId) => {
          const category = this.businessCategoryListBackups.find((item) => {
            return item.id === Number(categoryId)
          })
          return category ? category.name : ''
        })
        .join('、')
      this.userInfo.experience_skill = this.tempSelectedCategoryIds.join(',')
      this.$refs.popup.close()
    },

    // 获取城市列表
    getCityList() {
      let that = this
      getCityListOpen().then((res) => {
        this.district = res.data
        that.initialize()
      })
    },

    // 初始化地区选择器
    initialize() {
      let that = this,
        province = [],
        city = [],
        area = []

      // 处理省份数据
      that.district.forEach((item, i) => {
        province.push(item.n)
        if (item.n === this.region[0]) {
          this.valueRegion[0] = i
          this.multiIndex[0] = i
        }
      })

      // 处理城市数据
      that.district[this.valueRegion[0]].c.forEach((item, i) => {
        if (this.region[1] === item.n) {
          this.valueRegion[1] = i
          this.multiIndex[1] = i
        }
        city.push(item.n)
      })

      // 处理区县数据
      that.district[this.valueRegion[0]].c[this.valueRegion[1]].c.forEach((item, i) => {
        if (this.region[2] === item.n) {
          this.valueRegion[2] = i
          this.multiIndex[2] = i
        }
        area.push(item.n)
      })

      // 设置多列选择器数据
      this.multiArray = [province, city, area]
    },

    // 地区选择
    bindRegionChange(e) {
      let multiIndex = this.multiIndex,
        province = this.district[multiIndex[0]] || { c: [] },
        city = province.c[multiIndex[1]] || { v: 0 },
        district = city.c[multiIndex[2]] || { v: 0 },
        multiArray = this.multiArray,
        value = e.detail.value

      this.region = [multiArray[0][value[0]], multiArray[1][value[1]], multiArray[2][value[2]]]

      this.cityId = city.v
      this.districtId = district.v
      this.provinceId = province.v

      this.valueRegion = [0, 0, 0]
      this.initialize()
    },

    // 地区选择器列变化
    bindMultiPickerColumnChange(e) {
      let that = this,
        column = e.detail.column,
        value = e.detail.value,
        currentCity = this.district[value] || { c: [] },
        multiArray = that.multiArray,
        multiIndex = that.multiIndex
      multiIndex[column] = value
      switch (column) {
        case 0:
          let areaList = currentCity.c[0] || { c: [] }
          multiArray[1] = currentCity.c.map((item) => {
            return item.n
          })
          multiArray[2] = areaList.c.map((item) => {
            return item.n
          })
          break
        case 1:
          let cityList = that.district[multiIndex[0]].c[multiIndex[1]].c || []
          multiArray[2] = cityList.map((item) => {
            return item.n
          })
          break
        case 2:
          break
      }
      // #ifdef MP || APP-PLUS
      this.$set(this.multiArray, 0, multiArray[0])
      this.$set(this.multiArray, 1, multiArray[1])
      this.$set(this.multiArray, 2, multiArray[2])
      // #endif
      // #ifdef H5
      this.multiArray = multiArray
      // #endif

      this.multiIndex = multiIndex
      // this.setData({ multiArray: multiArray, multiIndex: multiIndex});
    },
    flattenCategories(categories, result = []) {
      categories.forEach((category) => {
        result.push({
          id: category.id,
          name: category.name,
          level: category.level
        })

        if (category.children && category.children.length > 0) {
          this.flattenCategories(category.children, result)
        }
      })
      return result
    },

    // 提交表单
    formSubmit(e) {
      let that = this,
        value = e.detail.value
      console.log('🚀 ~ formSubmit ~ value:', value)
      // 表单验证
      if (!value.card_id) return that.$util.Tips({ title: that.$t('请输入身份证号') })
      if (!value.real_name) return that.$util.Tips({ title: that.$t('请输入真实姓名') })
      if (!value.contact_type) return that.$util.Tips({ title: that.$t('请输入紧急联系人关系') })
      if (!value.contact_name) return that.$util.Tips({ title: that.$t('请输入紧急联系人姓名') })
      if (!value.contact_phone) return that.$util.Tips({ title: that.$t('请输入紧急联系人电话') })
      if (this.region[0] === that.$t('省')) return that.$util.Tips({ title: that.$t('请选择所在地区') })
      if (!this.userInfo.experience_skill) return that.$util.Tips({ title: that.$t('请选择服务技能') })
      if (!this.userInfo.experience_year) return that.$util.Tips({ title: that.$t('请选择工作年限') })

      // 构建提交数据
      let submitData = {
        province: this.provinceId,
        city: this.cityId,
        district: this.districtId,
        card_id: value.card_id,
        real_name: value.real_name,
        contact_type: value.contact_type,
        contact_name: value.contact_name,
        contact_phone: value.contact_phone,
        experience_skill: this.userInfo.experience_skill,
        experience_year: this.userInfo.experience_year,
        introduction: value.introduction
      }
      // 调用API提交数据
      uni.showLoading({ title: that.$t('提交中') })
      // 使用API调用提交数据
      updateEnterpriseInfo(this.uid, submitData)
        .then((res) => {
          uni.hideLoading()
          return that.$util.Tips(
            {
              title: res.msg,
              icon: 'success'
            },
            {
              tab: 3,
              url: 1
            }
          )
        })
        .catch((msg) => {
          uni.hideLoading()
          return that.$util.Tips({ title: msg || that.$t('提交失败') })
        })
    },
    // 获取获取经营品类
    loadBusinessCategories() {
      getSkillTree().then((res) => {
        // 保持经营品类的层级结构
        this.businessCategoryList = res.data
        this.businessCategoryListBackups = this.flattenCategories(res.data)
      })
    },
    handleYearChange(e) {
      const index = e.detail.value
      this.yearIndex = index
      this.userInfo.experience_year = Number(index) + 1
    }
  }
}
</script>

<style scoped lang="scss">
/* 钉钉风格样式 */
.enterprise-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 40rpx;
}

/* 头部标题 */
.header {
  background-color: #fff;
  padding: 40rpx 30rpx;
  margin-bottom: 20rpx;

  .title {
    font-size: 36rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 10rpx;
  }

  .subtitle {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .status-tag {
      padding: 4rpx 16rpx;
      border-radius: 24rpx;
      font-size: 32rpx;

      &.status-pending {
        background-color: #fff3e6;
        color: #fa8c16;
      }

      &.status-certified {
        background-color: var(--view-theme);
        color: #fff;
      }

      &.status-reviewing {
        background-color: #f6ffed;
        color: #52c41a;
      }
    }
  }
}

/* 表单内容 */
.form-content {
  .form-section {
    background-color: #fff;
    margin-bottom: 20rpx;

    .section-title {
      font-size: 28rpx;
      color: #666;
      padding: 30rpx 30rpx 10rpx;
      font-weight: 500;
    }

    .form-item {
      display: flex;
      padding: 24rpx 30rpx;
      border-bottom: 1rpx solid #f5f5f5;

      &:last-child {
        border-bottom: none;
      }

      .label {
        width: 230rpx;
        font-size: 28rpx;
        color: #333;
        padding-top: 6rpx;
        flex-shrink: 0;

        .required {
          color: #ff4d4f;
          margin-left: 4rpx;
        }
      }

      .content {
        flex: 1;

        .input {
          width: 100%;
          height: 60rpx;
          font-size: 28rpx;
          color: #333;
        }

        .picker-content {
          display: flex;
          justify-content: space-between;
          align-items: center;
          height: 60rpx;
          font-size: 28rpx;
          color: #333;

          .icon-arrow-right {
            color: #ccc;
            font-size: 24rpx;
          }
        }
      }

      /* 头像上传样式 */
      .avatar-content {
        display: flex;
        align-items: center;

        .avatar-wrapper {
          position: relative;
          width: 120rpx;
          height: 120rpx;
          border-radius: 60rpx;
          overflow: hidden;
          background-color: #f5f5f5;

          .avatar {
            width: 100%;
            height: 100%;
          }

          .avatar-edit {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 40rpx;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;

            .icon-camera {
              color: #fff;
              font-size: 24rpx;
            }
          }
        }
      }

      /* 营业执照上传样式 */
      &.license-item {
        flex-direction: column;

        .label {
          width: 100%;
          margin-bottom: 20rpx;
        }

        .license-content {
          width: 100%;

          .license-wrapper {
            position: relative;
            width: 240rpx;
            height: 320rpx;
            border-radius: 8rpx;
            overflow: hidden;
            background-color: #f5f5f5;
            margin-bottom: 10rpx;

            .license-img {
              width: 100%;
              height: 100%;
            }

            .license-delete {
              position: absolute;
              top: 10rpx;
              right: 10rpx;
              width: 40rpx;
              height: 40rpx;
              border-radius: 20rpx;
              background-color: rgba(0, 0, 0, 0.5);
              display: flex;
              justify-content: center;
              align-items: center;

              .icon-delete {
                color: #fff;
                font-size: 24rpx;
              }
            }
          }

          .license-upload {
            width: 240rpx;
            height: 320rpx;
            border-radius: 8rpx;
            border: 1rpx dashed #ddd;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            margin-bottom: 10rpx;

            .icon-add {
              font-size: 48rpx;
              color: #999;
              margin-bottom: 10rpx;
            }

            .upload-text {
              font-size: 24rpx;
              color: #999;
            }
          }

          .license-tip {
            font-size: 24rpx;
            color: #999;
          }
        }
      }
    }
  }
}

/* 提交按钮 */
.submit-btn-wrapper {
  padding: 40rpx 30rpx;

  .submit-btn {
    width: 100%;
    height: 90rpx;
    background-color: var(--view-theme);
    color: #fff;
    font-size: 32rpx;
    border-radius: 8rpx;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}

.placeholder {
  color: #ccc;
}

/* 经营品类选择弹窗样式 */
.category-popup {
  background-color: white;
  border-radius: 20rpx 20rpx 0 0;
  .category-popup-content {
    width: 100%;
    .category-popup-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 30rpx;
      border-bottom: 1rpx solid #f5f5f5;

      .category-popup-title {
        font-size: 32rpx;
        font-weight: 500;
        color: #333;
      }

      .category-popup-close {
        width: 60rpx;
        height: 60rpx;
        display: flex;
        justify-content: center;
        align-items: center;

        .icon-guanbi {
          font-size: 32rpx;
          color: #999;
        }
      }
    }

    .category-popup-body {
      max-height: 60vh;
      overflow-y: auto;
      padding: 20rpx 30rpx;

      .category-item {
        display: flex;
        // align-items: center;
        padding: 20rpx 0;
        border-bottom: 1rpx solid #f5f5f5;

        checkbox {
          margin-right: 20rpx;
        }

        .category-name {
          font-size: 28rpx;
          color: #333;
        }

        &.level-1 {
          font-weight: 500;
        }

        &.level-2 {
          padding-left: 40rpx;
          font-weight: normal;
        }

        &.level-3 {
          padding-left: 80rpx;
          font-weight: normal;
          font-size: 26rpx;
        }
      }

      .sub-categories {
        border-top: 1rpx dashed #f5f5f5;
      }

      .sub-sub-categories {
        margin-top: 10rpx;
      }
    }

    .category-popup-footer {
      display: flex;
      border-top: 1rpx solid #f5f5f5;

      .category-popup-btn {
        flex: 1;
        height: 90rpx;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 30rpx;

        &.cancel {
          color: #666;
          background-color: #f5f5f5;
        }

        &.confirm {
          color: #fff;
          background-color: var(--view-theme);
        }
      }
    }
  }
}

.textarea {
  width: 100%;
  height: 180rpx;
  font-size: 28rpx;
  color: #333;
  padding: 20rpx;
  background-color: #f5f5f5;
  border-radius: 8rpx;
  box-sizing: border-box;
}
// 垂直
.vertical-wrap {
  display: block !important;
  .label {
    margin-bottom: 20rpx;
  }
}
</style>
