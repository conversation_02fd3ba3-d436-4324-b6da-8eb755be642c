<template>
  <view class="team-page" :style="colorStyle">
    <!-- 顶部统计 -->
    <view class="team-stats">
      <view class="stats-item">
        <text class="stats-num">{{ teamList.length }}</text>
        <text class="stats-label">团队成员</text>
      </view>
      <view class="stats-item">
        <text class="stats-num">{{ confirmedMembers }}</text>
        <text class="stats-label">已确认成员</text>
      </view>
    </view>

    <!-- 成员列表 -->
    <view class="member-list">
      <view class="list-header">
        <text class="title">成员列表</text>
        <button
          class="add-btn"
          hover-class="btnHoverClass"
          @click="showAddMemberPopup"
        >
          <text class="iconfont icon-tianjia"></text>添加成员
        </button>
      </view>

      <view class="member-item" v-for="item in teamList" :key="item.id">
        <view class="member-avatar">
          <text>{{ item.user_real_name.substring(0, 1) }}</text>
        </view>
        <view class="member-info">
          <view class="member-main">
            <text class="member-name">{{ item.user_real_name }}</text>
            <text
              class="member-status"
              :class="item.is_confirm ? 'confirmed' : 'pending'"
            >
              {{ item.is_confirm ? "已确认" : "待确认" }}
            </text>
          </view>
          <view class="member-sub">
            <text class="member-phone">{{ item.user_phone }}</text>
            <text class="member-note" v-if="item.note">({{ item.note }})</text>
          </view>
          <view class="member-skills">
            <text
              class="skill-tag"
              v-for="(skill, index) in item.user_experience_skill.split(',')"
              :key="index"
            >
              {{ skill }}
            </text>
          </view>
        </view>
        <view class="member-actions">
          <button class="delete-btn" @tap="confirmDelete(item)">
            <text class="iconfont icon-shanchu"></text>
          </button>
        </view>
      </view>

      <view class="no-data" v-if="teamList.length === 0">
        <image src="/static/images/no-thing.png" mode="aspectFit"></image>
        <text>暂无团队成员</text>
      </view>
    </view>

    <!-- 添加成员弹窗 -->
    <uni-popup ref="addMemberPopup" type="center">
      <view class="popup-content">
        <view class="popup-header">
          <text class="popup-title">添加成员</text>
          <text class="popup-close" @tap="closeAddMemberPopup">×</text>
        </view>
        <view class="popup-body">
          <view class="form-item">
            <text class="label">手机号</text>
            <input
              type="number"
              v-model="addForm.phone"
              placeholder="请输入成员手机号"
              maxlength="11"
            />
          </view>
          <view class="form-item">
            <text class="label">备注</text>
            <input
              type="text"
              v-model="addForm.note"
              placeholder="请输入备注信息（选填）"
            />
          </view>
        </view>
        <view class="popup-footer">
          <button class="cancel-btn" @tap="closeAddMemberPopup">取消</button>
          <button class="confirm-btn" @tap="handleAddMember">确定</button>
        </view>
      </view>
    </uni-popup>

    <!-- 提示窗示例 -->
    <uni-popup ref="deletePopup" type="dialog">
      <uni-popup-dialog
        type="warn"
        cancelText="取消"
        confirmText="确定"
        title="提示"
        content="确定删除该成员吗？"
        colorStyle="color: var(--view-theme)"
        @confirm="handleDelete"
        @close="closeDeletePopup"
      ></uni-popup-dialog>
    </uni-popup>
  </view>
</template>

<script>
import { getTeamList, addTeamMember, delTeamMember } from "@/api/worker";
import colors from "@/mixins/color";

export default {
  mixins: [colors],

  data() {
    return {
      teamList: [],
      page: 1,
      limit: 20,
      addForm: {
        phone: "",
        note: "",
      },
      deleteItem: null,
    };
  },

  computed: {
    confirmedMembers() {
      return this.teamList.filter((item) => item.is_confirm).length;
    },
  },

  onLoad() {
    this.loadTeamList();
  },

  methods: {
    // 加载团队列表
    async loadTeamList() {
      const res = await getTeamList({
        page: this.page,
        limit: this.limit,
      });
      console.log("🚀 ~ loadTeamList ~ res:", res);
      if (res.status === 200) {
        this.teamList = res.data.list;
      }
    },

    // 显示添加成员弹窗
    showAddMemberPopup() {
      this.$refs.addMemberPopup.open();
    },

    // 关闭添加成员弹窗
    closeAddMemberPopup() {
      this.$refs.addMemberPopup.close();
      this.addForm = {
        phone: "",
        note: "",
      };
    },

    // 添加成员
    async handleAddMember() {
      if (!this.addForm.phone) {
        this.$util.Tips({ title: "请输入手机号" });
        return;
      }
      if (!/^1\d{10}$/.test(this.addForm.phone)) {
        this.$util.Tips({ title: "请输入正确的手机号" });
        return;
      }

      try {
        const res = await addTeamMember(this.addForm);
        if (res.status === 200) {
          this.$util.Tips({ title: res.msg });
          this.closeAddMemberPopup();
          this.loadTeamList();
        }
      } catch (error) {
        this.$util.Tips({ title: error.msg });
      }
    },

    // 显示删除确认弹窗
    confirmDelete(item) {
      this.deleteItem = item;
      this.$refs.deletePopup.open();
    },

    // 关闭删除确认弹窗
    closeDeletePopup() {
      this.$refs.deletePopup.close();
      this.deleteItem = null;
    },

    // 删除成员
    async handleDelete() {
      if (!this.deleteItem) return;

      try {
        const res = await delTeamMember(this.deleteItem.id);
        if (res.status === 200) {
          this.$util.Tips({ title: "删除成功", icon: "success" });
          this.closeDeletePopup();
          this.loadTeamList();
        }
      } catch (error) {
        this.$util.Tips({ title: error.msg });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.team-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 20rpx;
}

.team-stats {
  display: flex;
  background-color: #fff;
  padding: 30rpx;
  border-radius: 12rpx;
  margin-bottom: 20rpx;

  .stats-item {
    flex: 1;
    text-align: center;

    &:first-child {
      border-right: 1px solid #eee;
    }

    .stats-num {
      display: block;
      font-size: 36rpx;
      font-weight: bold;
      color: #333;
      margin-bottom: 10rpx;
    }

    .stats-label {
      font-size: 26rpx;
      color: #666;
    }
  }
}

.member-list {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;

  .list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30rpx;

    .title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
    }

    .add-btn {
      display: flex;
      align-items: center;
      background-color: var(--view-theme);
      color: #fff;
      font-size: 28rpx;
      padding: 12rpx 24rpx;
      border-radius: 30rpx;

      .iconfont {
        margin-right: 8rpx;
        font-size: 28rpx;
      }
    }
  }

  .member-item {
    display: flex;
    align-items: center;
    padding: 20rpx 0;
    border-bottom: 1px solid #f5f5f5;

    &:last-child {
      border-bottom: none;
    }

    .member-avatar {
      width: 80rpx;
      height: 80rpx;
      background-color: var(--view-theme);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 20rpx;

      text {
        color: #fff;
        font-size: 32rpx;
      }
    }

    .member-info {
      flex: 1;
      margin-right: 20rpx;

      .member-main {
        display: flex;
        align-items: center;
        margin-bottom: 8rpx;

        .member-name {
          font-size: 30rpx;
          color: #333;
          margin-right: 12rpx;
        }

        .member-status {
          font-size: 24rpx;
          padding: 4rpx 12rpx;
          border-radius: 20rpx;

          &.confirmed {
            background-color: #e8f5e9;
            color: #4caf50;
          }

          &.pending {
            background-color: #fff3e0;
            color: #ff9800;
          }
        }
      }

      .member-sub {
        font-size: 26rpx;
        color: #666;
        margin-bottom: 8rpx;

        .member-note {
          margin-left: 8rpx;
          color: #999;
        }
      }

      .member-skills {
        display: flex;
        flex-wrap: wrap;
        gap: 8rpx;

        .skill-tag {
          font-size: 24rpx;
          color: #666;
          background-color: #f5f5f5;
          padding: 4rpx 12rpx;
          border-radius: 4rpx;
        }
      }
    }

    .member-actions {
      .delete-btn {
        background: none;
        padding: 20rpx;

        .iconfont {
          font-size: 36rpx;
          color: #ff4d4f;
        }
      }
    }
  }
}

.no-data {
  padding: 60rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  image {
    width: 400rpx;
    height: 400rpx;
    margin-bottom: 20rpx;
  }

  text {
    font-size: 28rpx;
    color: #999;
  }
}

.popup-content {
  background-color: #fff;
  border-radius: 12rpx;
  width: 600rpx;

  .popup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10rpx 28rpx;
    border-bottom: 1px solid #eee;

    .popup-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
    }

    .popup-close {
      font-size: 40rpx;
      color: #999;
      padding: 10rpx;
    }
  }

  .popup-body {
    padding: 30rpx;

    .form-item {
      margin-bottom: 20rpx;

      .label {
        display: block;
        font-size: 28rpx;
        color: #333;
        margin-bottom: 12rpx;
      }

      input {
        height: 80rpx;
        border: 1px solid #eee;
        border-radius: 8rpx;
        padding: 0 20rpx;
        font-size: 28rpx;
      }
    }
  }

  .popup-footer {
    display: flex;
    border-top: 1px solid #eee;
    border-radius: 0 0 12rpx 12rpx;
    button {
      flex: 1;
      height: 90rpx;
      line-height: 90rpx;
      text-align: center;
      font-size: 30rpx;
      border-radius: 0;
      &:first-child {
        border-radius: 0 0 0 12rpx;
      }
      &:last-child {
        border-radius: 0 0 12rpx 0;
      }

      &.cancel-btn {
        background-color: #fff;
        color: #666;
      }

      &.confirm-btn {
        background-color: var(--view-theme);
        color: #fff;
      }
    }
  }
}
</style>
