<template>
  <view class="advise-detail" :style="colorStyle">
    <!-- 状态展示 -->
    <view class="status-card">
      <view class="status-text" :class="detail.status === 0 ? 'pending' : 'replied'">
        {{ detail.status_txt }}
      </view>
      <view class="time">提交时间：{{ detail.add_time }}</view>
    </view>
    <!-- 回复内容 -->
    <view class="reply-card" v-if="detail.reply">
      <view class="card-title">官方回复</view>
      <view class="reply-content">{{ detail.reply }}</view>
      <view class="reply-time">回复时间：{{ detail.reply_time }}</view>
    </view>
    <!-- 反馈内容 -->
    <view class="content-card">
      <view class="card-title">反馈内容</view>
      <view class="content-text">{{ detail.content }}</view>
      <view class="image-list" v-if="detail.images">
        <image v-for="(img, index) in imageList" :key="index" :src="img" mode="aspectFill" @tap="previewImage(index)" />
      </view>
    </view>

    <!-- 联系信息 -->
    <view class="contact-card">
      <view class="card-title">联系信息</view>
      <view class="info-item">
        <text class="label">联系人：</text>
        <text class="value">{{ detail.name }}</text>
      </view>
      <view class="info-item">
        <text class="label">联系电话：</text>
        <text class="value">{{ detail.phone }}</text>
      </view>
    </view>
  </view>
</template>

<script>
import colors from '@/mixins/color.js'
import { adviseDetail } from '@/api/customer.js'

export default {
  mixins: [colors],
  data() {
    return {
      detail: {},
      imageList: []
    }
  },
  onLoad(options) {
    if (options.id) {
      this.loadData(options.id)
    }
  },
  methods: {
    async loadData(id) {
      try {
        const res = await adviseDetail(id)
        if (res.data) {
          this.detail = res.data
          // 处理图片列表
          this.imageList = this.detail.images ? this.detail.images.split(',') : []
        }
      } catch (error) {
        uni.showToast({
          title: '加载失败',
          icon: 'none'
        })
      }
    },
    // 预览图片
    previewImage(index) {
      uni.previewImage({
        current: index,
        urls: this.imageList
      })
    }
  }
}
</script>

<style lang="scss">
.advise-detail {
  min-height: 100vh;
  background: #f5f5f5;
  padding: 20rpx;

  .status-card,
  .content-card,
  .contact-card,
  .reply-card {
    background: #fff;
    border-radius: 12rpx;
    padding: 30rpx;
    margin-bottom: 20rpx;
  }

  .status-card {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .status-text {
      font-size: 32rpx;
      font-weight: bold;
      &.pending {
        color: var(--view-theme);
      }
      &.replied {
        color: #07c160;
      }
    }

    .time {
      font-size: 24rpx;
      color: #999;
    }
  }

  .card-title {
    font-size: 30rpx;
    font-weight: bold;
    margin-bottom: 20rpx;
    color: #333;
  }

  .content-text {
    font-size: 28rpx;
    color: #333;
    line-height: 1.6;
  }

  .image-list {
    display: flex;
    flex-wrap: wrap;
    margin-top: 20rpx;

    image {
      width: 220rpx;
      height: 220rpx;
      margin-right: 10rpx;
      margin-bottom: 10rpx;
      border-radius: 8rpx;
    }
  }

  .info-item {
    display: flex;
    margin-bottom: 16rpx;
    font-size: 28rpx;

    .label {
      color: #666;
      width: 140rpx;
    }

    .value {
      color: #333;
      flex: 1;
    }
  }

  .reply-card {
    .reply-content {
      font-size: 28rpx;
      color: #333;
      line-height: 1.6;
      margin-bottom: 16rpx;
    }

    .reply-time {
      font-size: 24rpx;
      color: #999;
    }
  }
}
</style>
