<template>
	<view :style="colorStyle">
		<view class="OrderCancellation">
			<view class="header"></view>
			<view class="whiteBg">
				<view class="input">
					<input type="number" placeholder="请输入核销码" v-model="verify_code" />
				</view>
				<view class="bnt" @click="codeChange">{{ $t(`立即核销`) }}</view>
			</view>
			<!-- #ifdef MP || MP-WEIXIN || APP-PLUS -->
			<view class="scan" @click="scanCode">
				<image src="../static/scan.gif"></image>
			</view>
			<!-- #endif -->
			<!-- #ifdef H5  -->
			<view v-if="isWeixin" class="scan" @click="scanCode">
				<image src="../static/scan.gif"></image>
			</view>
			<!-- #endif -->
		</view>
		<view v-if="iShidden">
			<view class="WriteOff">
				<view class="pictrue">
					<image :src="orderInfo.image" />
				</view>
				<view class="num acea-row row-center-wrapper">
					<text>{{ orderInfo.order_id }}</text>
					<view class="views" @click="goOrderDetails(orderInfo.order_id, orderInfo.order_type)">
						{{ $t(`查看`) }}
						<text class="iconfont icon-jiantou views-jian"></text>
					</view>
				</view>
				<view class="tip">{{ $t(`确定要核销此订单吗`) }}</view>
				<view class="btn sure" @click="confirm">{{ $t(`确定核销`) }}</view>
				<view class="btn cancel" @click="cancel">{{ $t(`取消`) }}</view>
			</view>
			<view class="mask"></view>
		</view>
		<!-- #ifndef MP -->
		<home></home>
		<!-- #endif -->
	</view>
</template>

<script>
import { orderVerific } from '@/api/admin';
import home from '@/components/home';
import colors from '@/mixins/color.js';
import { mapGetters } from 'vuex';
import { toLogin } from '@/libs/login.js';
export default {
	components: {
		home
	},
	mixins: [colors],
	computed: mapGetters(['isLogin']),
	data() {
		return {
			iShidden: false,
			verify_code: '',
			isWeixin: '',
			orderInfo: {}
		};
	},
	onLoad(options) {
		if (!this.isLogin) return toLogin();
		// #ifdef H5 || APP-PLUS
		this.isWeixin = this.$wechat.isWeixin();
		this.verify_code = options.verify_code || '';
		// #endif
		// #ifdef MP
		if (options.scene) {
			let value = this.$util.getUrlParams(decodeURIComponent(options.scene));
			this.verify_code = value.verify_code || '';
		}
		// #endif
	},
	methods: {
		/**
		 * 去订单详情
		 */
		goOrderDetails: function (id, type) {
			if (type == 'integral') {
				uni.navigateTo({
					url: '/pages/points_mall/integral_order_details?order_id=' + id
				});
			} else {
				uni.navigateTo({
					url: '/pages/goods/admin_order_detail/index?id=' + id + '&goname=look'
				});
			}
		},
		// 立即核销
		codeChange: function () {
			let self = this;
			let ref = /[0-9]{12}/;
			if (!this.verify_code)
				return self.$util.Tips({
					title: this.$t(`请输入核销码`)
				});
			if (!ref.test(this.verify_code))
				return self.$util.Tips({
					title: this.$t(`请输入正确的核销码`)
				});
			self.$util.Tips({
				title: this.$t(`查询中`)
			});
			setTimeout(() => {
				orderVerific(this.verify_code, 0)
					.then((res) => {
						self.orderInfo = res.data;
						self.iShidden = true;
					})
					.catch((res) => {
						self.verify_code = '';
						return self.$util.Tips({
							title: res
						});
					});
			}, 800);
		},
		// 扫码核
		scanCode() {
			var self = this;
			// #ifdef MP || APP-PLUS
			uni.scanCode({
				success(res) {
					let path = decodeURIComponent(res.path);
					self.verify_code = path.split('code=')[1];
					self.codeChange();
				},
				fail(res) {}
			});
			// #endif
			//#ifdef H5
			this.$wechat
				.wechatEvevt('scanQRCode', {
					needResult: 1,
					scanType: ['qrCode', 'barCode']
				})
				.then((res) => {
					if (res.scanType == 'WX_CODE') {
						self.verify_code = res.path.split('%3D')[1];
					} else {
						self.verify_code = res.resultStr.split('=')[1];
					}
					this.codeChange();
				});
			//#endif
		},

		/**
		 * 确定销码
		 */
		confirm: function () {
			let self = this;
			orderVerific(this.verify_code, 1)
				.then((res) => {
					self.verify_code = '';
					self.iShidden = false;
					self.$util.Tips({
						title: res.msg
					});
				})
				.catch((res) => {
					self.$util.Tips({
						title: res
					});
				});
		},
		/**
		 * 取消
		 */
		cancel: function () {
			this.iShidden = false;
		}
	}
};
</script>

<style lang="scss">
page {
	background-color: #fff;
}

.OrderCancellation .header {
	background-image: url('data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAMCAgMCAgMDAwMEAwMEBQgFBQQEBQoHBwYIDAoMDAsKCwsNDhIQDQ4RDgsLEBYQERMUFRUVDA8XGBYUGBIUFRT/2wBDAQMEBAUEBQkFBQkUDQsNFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBT/wgARCAEsAu4DAREAAhEBAxEB/8QAHQABAAIBBQEAAAAAAAAAAAAAAgABBgMEBQcICf/EAB0BAQEAAgMBAQEAAAAAAAAAAAACAQUEBgcDCAn/2gAMAwEAAhADEAAAAM9/N/61JCLJRQV0UolWpZCUslKIQ1ZKCsqJQF0yVFRDYqLJWAqIVkKxdEIVkKyo0CyFRZCiFRUWSo3kslQKWQqIVkK6sVEoKiySllVUJRFgopcAqgkKKX6i1XikCRZCQK4FQJYroJALpRCFdUBSyqghWQrIVGxUWSsBUQrIFlRsVkIVlRoFkKyFQCshWbFRZKiFZCohWQ3dBUSgrISl0GqJS6CQK4ApVFECv1FqvFCQiwUEi6KUQ2opZCuilEIaslBWVEoKyFZCo2KioMlmlAKwFZuiFZCFZUKyVEKyFQZKiFZsVEKyFRZKiFZKWbFRKCshXRVUSAZpRIFZKIoEIFfqLVeKEhFkoBF0UolWJSyFdFKIQrqhCsqJQVlkqIVGxWQLIVFkqLJULohUWSyVFQoVkKyFQCshWbFRCshWQqIVkN3QVUEKyEpRq6ISMc5w9bzPE1+/+PG1Z+enm9h9uTwvL2OO87b7evpRS/UWq8UJCLBAlLgVEoq7ISlkpRCGroIVlVBCshWQqNisFLAVEKyyVBRsSlRQxczOjVhZCshUArIVm6IWSohWQqIVkN3QVEoKyFdFVRY5fh8HL9XoNx8vlLSEIQho3eG7TsON87bxfqDVeKEhFgogVwKiUSwWSgrigEK6oQrKiUFZCshUbBdAUWSohWQLN1WGn8ORo8PlaXy+qxislyvlXN+Gl9MlRZKgFZCsqNiyVEKiyVEKyFdWKqCFZKWSqrIdfqMv1WghCEIQhCEMT2W9wnZ9k9RarxSBKXQSilwKuK+fM4Lj7Taz9tzXx5rka7kPvxiUohDVkoKyqgFLBSyFRsVkC6Cva8fmH4fWMVyo1Pvx9rwdhpcP7UzWcrGNPNXgPovZ8arVxfs/mFNHmY0LyVGxUQrIVkKiFZCurFVBCshXRy/G1+e6Xq9kIQhCEIQhDA9p2nvb5+YEi4EJYF0cb8uXwHE2sIQhzXM127+/wpRCGrJQVlVACsshRZN0WSoslkY+vH6zZn53SpjED9cVF0zMKyGaucHObBeZKmTmpjExkbL57bm4KiyGSyVkpRCFZKWbFRKCuGmXjPZuh6ZvPn8IxGYWURiiyMwjEZjGabDrxIuAIEpZOF4Gx0Y+kJhMoalxyOy4tKIQrNKCsqo01BQVp3QUGSqmaZmXHa/bDj/WmTjNMTOYVnJZomchV4wQ1UxgZqYTObxiMjNDb8fa/SjmgzSzlTNYzAhWQrqxUSgrITIOHq+wdR1WQlpCWkJaQlpCEISkyy3YaKBIsECUujYazkTCEZjEIPbqUQhWaUFZUTTXpqy7cdfy7a6LCdV2Pn+bqyzWc0F9Op+meicf8eZ292rzztXsvRPMfnntfCcTawjPqX0jwvr/AEnbME0fZdfHyx75bnvTt/mGFavsnUnXfQOS+3C9WejeFdGdP9MwHUds2fw5VYerfUvA+jND6TjXE3PfXZ/Lun9F3/u/snm/QnWfUOztx0zzt1L1mLsAbugqJQVkK+w9P1HJeDp4WQhCFFkIQosoybZ6ckIslBKXCa2FGJhCEB9K2e15VKIQrNKCsqBpZ+mhV909t879tetfm/xx5j+gs93HV7zRM723V+O+HN8P+TfpnHeBt/V3o3hOfbrqXTPTfR+Oxz9DH2SPUPoPiHlroPt/U/Uu9dic/qffnoXlfc3a/OcQ1vYeI43Oy3ZaHlOVwuG4mw20/b5x+J/sDc4+P1h/RH4S899W9Vxbh7v1p3fw3wT5p+nvZ3fvz14x6F+hPQ/Z/Kfmx5L+wFGbAFdWKiUFZKO2tB0XlfhwYQhCEKLIQhCizntnrCRcAUQK4anH+e+1/EhCEOL2PP2nK5FKIQ1dBCsqDOg+ml9K767r5dtcfboHp3qehj6Femr3t67+X+uNL27yZ597oFfVL3r8SdF9Y9K6/wBV2r0n3Lx7hONsvPfU/WO8Oy+a5Dy9T8zPFP2RzPL4HfXa/L+xdz1HBNb2fDuB2DNtj1zANX2jh+PsOf5Os6D6t6j3p2fzPursfnPhfzn9LdgbXq3vr0v8vYRwOw9+dl8v8+dY9W6p0nePK3Svb3g5EF3QVEoKyE7n6755yHz4sYjMIxGYQhCMQhGYcvs+CSLgCEAuBVuePxuR4XC1o+ejf14rnc/YcrmUUohCs0oKyoM7bP10/pXvD2P8wZ/uerfL3wT9pwKtJf0190/G3UnXu+eHPLf0mM5+q3vf4i6e6/33q3R929K9z8d4XjbHoLqvqfdXZPOMm5mo+X3if7Py3YaP6o+4fibqPRd54Lj7HEuDv9/9OL3Z2PzrqPQ977V3fSPlF4j+4s62XW/ob6r+UNLFdPaLv3q/uniXze8s/XGK8Pd+pO3+Kdw7zoXyq8W/cDQoQK6sVEoKyE7l6/57zPH10IQhCEIQhCEIchtePRQVwoBFwKiGxXAhKXSiENXQQrKtNnbX9dq+3qP0XxX1B37xb5Yfn/8AbSCaL6fTT3T8b9R9e754b8u/Semr6s+9/iHofq/pnSHW/SfXffvA8f4u18wdM9p9Eds8lz7Z9a+Xfif7Ry3YaL6ne3/iXqDQd/zLYdd84dU9c7m3/nvbm86P5n6j7BnOx618zPH/ANkUfSv138e+f+t+p9dartn0u9Z/H/Uel7wcXlvN0PHfHm/KXxb9wajCYkDd0FRKCshX2lpuj5pwOuQhCEIQhMJlCEIbjaxAkWCAIuilENqXQSlkgFENWSgrKtNnbX9do+/qT0XxT1B37xb5Yfn/APbTiTdaK/pl7p+OOpOvd88N+XfpMK+rHvf4hwrW9hwXW9l7h7B5/wAd8+X1bo+7dj7jqOqx8ufE/wBo5hsdD9OPZvxr1tqO4dFdd9J8gdG989j97/Pvf/aPLsZ4W5671XbPFPnn6M707J5n9DPVPyhwHF2vUml736N7T5P8ffEf3rs/nyvYvefz96G7L5X8pfGP3DqzKYkCurFRKCslGT8PTdy6DzuEIQhCEIQhCEL3FEi4AogVwKoCxKWSlkpRCGroIVlQZ2t/XaPv6k9F8U9Qd+8W+WH5/wD22mCaL6fTT3T8b9R9e754d8u/SYZzDadeFXoV9PeXp/5ixXh7rxp55+hWnQ+f1w3Xdgzra9Z9/wDqn5b4PjbPozrvpPcO96Bz3K1fkfo/u/v7078tfLbxj9r4TruxrM/U/wBp/E3nPq/rXUmk759KPVvyF0tofQDi8z5/Xdr8+T8pPGP3BqzhMSBXLBRKCslLpjvjrnl3OcfWxiMwjEIzCMRmEIwGRuPtAkWSigrJFEqxXQQropRCGrJQVlQZ2z6bW/v6i9F8V9Qd/wDFfml4X+xeA4W0z3cdY083739e/LfT3W/QvEHl36RzDY9e+hXsP5UopnIebqNCfpwnE2dl5fMfxX9ldgbjqvpHuXj+ZbDQdHdc9J6B6x6j2Tuene1vQfzrxHx5/QXVvUvHPQf0CFfVH2j8S9Edd9L6k0vfPon6j+UPjT4R/QPffTj+2/Qfzj3PvfPPlT4v+4dRCJClmxVQQrIV0c9x9V3z13y54iELIUQshRCM9Ddg9Q7u4/nNFBXRCgqjJVAWiwEpZKUQhWaUFZUGdDN7avtnW86v6e7/AOM4xwdz5G83959teq/nCYra4+vm/pPr3WGg7pTOlV7d9NB9PX3ongeO8Xa+X+ke1KMNijsve9O4z583053XxfqjR967q7H5zgms7R5z6n61kPK1XpnuPjnhLzH9Nhn073Xxnu/sfm/XOr7XlvN0vgbzT9Q9n7jpnqbufiXR3XPSfO3VPW0XCFXZCom+3HwrOdOa4/Ufel1VZlr+ud16LzfVfOEIQhCEDjPTO79F6z2fdPUWq8UgSLJQFwgVEq1BWSlkpRCGroIVlRNJ9Nvd6D66WbpmsZ085NUMVeVsUyFaK9LN6WbLNiYhGdNWjm9OrDNMlmmaZhAq0s1p5sM1nNEZLN4zecXGbCG7oKuQ3vF2Gq5I+db3c8fS+V39Epxej5mQ8XUd0aPznIOLqoQhCEOH++w6S3vpOGczsNL9RarxQkCuGji8W4m72k/fn+Rq+V+3Dhp2pdBKWSlEIVmlBWVECtJeld6aioshRZDMLZpkM6d1prCjVUURkshQVpqKiybowq8xkshkKCios0zTJZrKYRcAUG73Ow+Wj8r0vjfK9m4mhxfpx+p5JxnW5kbDV8kkxnMuF17N+B1nIeNqN5HH21ffH+TtcH53ZsD5/aNLNwK/UWq8UJCl0bHj8jj+JyoXnHL7LhkNqZpRIsFKIQrNKCsqoCgrTUFG8lQCuilUFkshRULoYzWaoLJUKoBWGSoqoIVlksm8hRwOboKyFVBKWQrq3Jdh4u04H23HP+ehxvpstd9x865XsnE04rj9Tydvw/qCLBRS4BUCUQK/UWq8UgV0Enxxra75QH1vb7LkEqwXAhXRSiENWSgrKqCFYZKio2IFlmmSyVEKwG7KiEKyyVBQpSwFRZKiFZDdFZCFZZKiFRCslLNt7ufht+H9dDj3u9r8dvxfru9p8dL5Xxuk5QmgzRSyQK4AiiUQK/UWq8UJFwBDX+Hy1PnGy5XJGfpDTsSLISl0ohoVkK6ColBWQrIVGxWQLIVEKyFYUbFZCyVFk1QZLJWQKoCyFRZN0QrIVkKiFZKuyP7zp/O+X7Vwtnrvvt+L9NL42vvjT+db3a/DY6DkZvtuuc7y9Z19p+047w9s6nQn60RQIQK/UWq8UgVkogCLopRKsV0EJS6CohXVCFZVQQrIVkKjeSoshRCohWWSoKNishCsqNAshUWQohWWSoqNiFdAWQqIVkK6tqcmeR7Bxtnrvts9b99L53y3ZuHt+L9K+OcnaXMtnoMX4O5wPU9m5XkcHPtv1nY/PkdU6Du4xcCUQK/UWq8UJCl0EJFkijaoG7oJQV0ohDV0EKyolBWQqLJUbFZCsBUQrIVhRsVkIVlkVmlgKyBRZKiFZuiEKyFZCohWQrqxUTebb47bifXkuwcXbcH65D8NTmm069wXE2WA6js+4+nzz3b9Y3f1+GA6js+PcTa5xteuYvwdzwXE2hKKKX6i1XikCuAKIFdFKJVosBKCulEIaslBWVEJSiyVkKjYrLJUAqIVkCyybyQrIVlRDVgKyBVAWQrNiohWQrIVEKyUs2KqCFcM13nXed+2t6/0/aNLF55t+s8vyuDgmp7LivA3eY7Pr+abTr+McHcYFqOz7D58o4zRAr9RarxQkIslAIuBUSrRYCUslKIQ1ZKCsqJQVlkslkqNishUGSohWQKN2QhWWSoqFCshWQqAVkKzdEIVkKyFRCshXViolBWTk+Rw9CfrnW363kPO1WE6vsWG6zf5VsdLm+163wnF2WAajtGtfzz/AHHV8I1fY8Y4O5ogV+otV4oSFLJAEXRSiVaLAQropRCFZpQVlRKCshWQqNisgWQqIVkCyo2IVkKyyayVgKyFQCshWbyWSFZCsqIQrIV1YqJQV6lRnW461lWw0mI67fYPqux5BzdXnW361x/y5XX+o7Rox9c93HWOZ5PAwPVdlw7Xb/Tm6IFfqLVeKEi4EoBFwKoadqXQSlkKqCFZpQVlRKCshWQqNisshVAUQrIFm6IVkIVlk1kLIVkCiFZCs2KiFZCshUQrIV1YqJTJVynI4WYbTQ4Hqey8pyOFnm460cZ6/wBR2ji/hzc723Wsj52ownWdjwzWdgyrYaXkfvxOt9L22ECv1FqvFIEiyEgVwKiVYlLJALigENWSgrKiEpZCshUbFZCsBUQrIFlRsVkIVlksirIVkKgFZCs2KiFZCshUQrIV1YqJQVk5rl6/PNx1ndfT44FqOzY/xNrm+165lex0mIa7e4Nq+yZBzNXnm361sPlyevdP2rg+LsYQK/UWq8UJCLJRQV0UqAtSyEpZKUQhq6CFZUSgrIVkKjYrIVgKiFZAulCxCshWVGgUWSshUArIVm6IQrIVkKiFZKWbFRKCsnbPY+j4pr95inA3WY7PQ5ltOv4zwdxgWp7Nyn34efbjrFM9f6jtHE/DnUzx/wAeVRAr9RarxQkXAEARdFKJViuglLJSiENWQlLKiUBdBWQ3RCoshZCohWQrF0QqLJCssmshZCsgUQrIVFk3RCshWQqIVkN3QVUEKyLLLdnos323XOD4uywHT9n1vp8892/Wd59ePgWp7PjvD2ucbXrmR87UdNda9B0ZuFFL9RarxSBKXRQCLopRKtSyEiwUohDV0EKyolAXQVFkqNioslZAohWQLN0QslRCssislRZKyFQCshUbshCshWVEIVkN3QVEoKyXl2BuurYpr95t5+uebfrPM8rX4Jqey4nwN5mWz0GabPr2M8LcYDqez8f8eXRRAr9RarxQkCuFBKXRSiValkJSyUohDV0EKyolBUWSshUbFYCshUQrIFm6IVkIVlRoFkKyFQCsslksm6LJUQqKyEKyG7JSiEpZCujKtlpOwNz1bCtX2LDdZ2DKNhpc423XOE4uxwDU9o4vj84EUCECv1FqvFIEiwUQK4FUNO1LoJQV0ohCs0oKyohKWQrIVGxWQLIVEKyBZUbFZCFZUaBRZKyFQCshWbFRCoslZCohWQrqxUSgrIVklNTMa9x2PvOpTOMB1HaOC4uxpcAUqiiBX6i1XihIFcKCUuilQBLBZKWSlEIaslBWVUEKyFZCo3kqAVkKiFZAs3kslZCshUKFZCsgUQrIVmxUWSohUWSohWQrqxUSgqLJKXVUQlFLJRS4BUAQgV+otV4oSLooJRS6KUSg3dBKCulEIV1QhWVEIV0FZCo2KwUsBUQrIVi6IVFkhWVChWQrIFEKyFZDdEKyFZCohWQ3dBUSgrJSyVVEgClkopcColFECv1FqvFCQK4EJF0UqAsCLJSyFVBCs0oK6UAlLIVFkqNisFLKgEKyBZuiFkqIVmqBSwFZCoBWQrNDmiFZCsqIQrJQbsqJQVkpZKoVEpdBIFZKVAEIFf8A/8QAVBAAAAQDAwUFEgoJBAMBAAAAAAECAwQFEQYSIRMiMUFREBQyYXEVFjA1QEJUYGJzgZGSobHB0eEgIyQzUlNkcoKiBzQ2RFBjssLwQ4CToxdV4vH/2gAIAQEAAT8C/wBthEajwxCIMz4R0CYZCdVeUEgi1FuGkj1EDhm1dbTkC4L6J+MKQaDxKn8SpTThyjbiXjF0/wCAsw5uYngkIbJBYF0BSCWVDKofhDRijEv4eZ6SLE9PEDzq44GVSB6FcaahXX+AHpVTSZ3QatOvGhCmmmNOroeHvZytHRIqFrno06y6I49dwLEwpala9xK1J1hD17TgfU/LgQv7C8oXzx8OoXz4vEL20qClQZ10atKT1jZTlSNGj7xCmrlIa/CQ/wA8JjRo1YEMPwl5zHLwtZDRpHgIhePHRr1EPAQpX2H1PDM5VWPBIU6LGQ11V5JYH0J1dCprFBQUFBQNqqXUpnT1FtGnj1e4efd8w1GVKp1p2DVpw1K2Dbq/zT8HzDzGXiSNBabpbdZjweD4OnDxe/qUiqYabySCLqhWKjFBQUFBQFgfUpqvV1p83wa+Acf5kCuvzl6yH+Yerdru15PUNGOjulDyj4z+Dq4uP/MQrb6epIJu+7XZ1TdF0XRdF0XRd6LLZU9NTWTJoqjTeMPWUjmWlrPJmSSrgoQUnjJg0bkO1fQR3a3iIc68z7G/On2jnXmfY350+0c6807G/On2jnXmnY350+0c680ofyb/ALE+0PsrhnltuJuuIOh//pblnpMzOVvIW6ptaCqVNZCLsU0xCuuIfWtSSrdDjFE3kHXiFeMi5R4C/CYPj85CWWPbjoBl9b60KcTWhELRSNuS5AkOqdU5XSEMpbLOxULyV4GRGH2sniXBEpsjzUl7UTvvJ365uTrrptE/kfMNbJZfLZSuN276xXk9Ih2yfiG0KUsiUoiv00DnA+3/APT7xM5UmWxi4c1ms00ztFQ43kz2lq3KhmweVaSrftL5EfzP/wBCfSfmJGJYyuWvIv1u01n7BLYPmjGtQxLJs3MLxh2wcQ20tW+W1XSrSgkUp5tRu98rkc01XrtRE2C3vDOu7+vXEmqmS94kspVOovIIWTZ3b1TEysa9LYF2JVEIWTeoi6LAIozXafVNBQXRQXRQGVOi2QduTa79NBkDKpUEnnzMjafhnG1rPLGeb4vUOfeF+oe8w594X6h7zDn4hfqHvMG7QsONpVk3cSroL2iKtRDwrd82nTxpoL2iYPJjI599OaTizVQ0i73XmEikMfK399IyTt9FLpqoIyexUJEFDuwJLUtNaNLqG7LRyzvJQltB6lr9g5yZh1qmT5THORMfs/lH7AViJh/J8Cz9gQ5M2W0oRBQ11JUIifP2C0m/lxzcRGQ+QQRXUkR3i8YN1K1FVV1O2ggYWDmMWxDoWbalddTSFWJZWi7vlfiErgClkC3DJUayRXE+WondnkTtTRrdU3k66CEo5l8z2EKVCKcSgr1btfCFcyUFePeaS2ndHNmX9nQ3/KkORsoeVeW/BLVtUtBjfEl+tgPKQHMnlF5x6dRCHZKJfbZQazWtV0sAkrqSLYH4+Ccnqss6xRsrmeohP4yC3zK1QjjC1JiCNWRMj9G5Y9vI2miG/oJWnziZ9LIvvS/QP0fM3plEO6ktU8Z+4WydyVn4juzSnz9FhU0h0cnVKTFBQUFBQLPohFeMiDFl5pCu5RpxpCy1kr3CPOey1F915Zt/TQdSC1m4tSlHVR4mfwJbLiXLoVW1pPoFrIPe8ubV/NIvMe7CfqjP3C9AtBHtS+YNqcP/AE9BaTxMLt4osGoQqd0sSaPOZy1qJUkkGuuBctBaGbqk0Eh5CCcM13KHyH7Bz/P9it+MxZyernaHzW2lvJmXBFoEE5JYwlFWjZnuQTrzUW0qGMyfrRFNoN60/wDO8lIlMbM4qAbdN8zM69aW0WgnEzl62aRJpvV60hZaDONOKVsu+sT+X72lTq+T0iGsjERMO06TqaOJJWjaFWbeTMkwWUTfUjKVD1j4hhlbhupohJq0bljoxiBmjjkQ4lpOSMiNW2pCdW2YQwpuBq66eGU0EkGdcRZ+AKZzVpg1m1pVeLiHMiN/9w//AMaRILMPyyYrjH4hLi1kdSSW0RLO+IZ1qtL6TTUQNkX5YS96TRbN/T8SRi1Eum7EHWIi9+QhKqdCpTosP8w390uqkroCUR69w1EWsKcr0UlXVEewSOec2st8TkcnTrq1rX2CLbJ6FeQrQpJl8GUdKYLvKPQLcdKWu/F6FbsH+qM/cL0Cf2aOdRDbqX8ldTdxTUc4DnZqfI94k8AcrlzUMa8pcrnU46ifyc51BoYJ3JXV360rqP2j/wAfudmp/wCP3izshVI0PEbxO5SnW0E96Sxvelbkn6bwPf0f1bllqHI2OVf9Zi1MiKawmVSoyeYSZpLUoWAYuy5936blPEQt49k5QhGtbpesStvJS2ER9FpJeYJ+Ntur+XC+v3ibqJuVRitjK/RuSLJ814XLXclfzr+gEmUGVSKC/KD5jp07yLyB8m59YPeuSub3P5mlK52zcjrazN15ZNuJYSR6EJ9o57Zt2YrySFiJtFzTfu+njduXLvnFoSrI47vR9FgzvQyOT+FmLC/vv4P7g780vk+DKOlMF3lHoFuelLXfy9Ct2D/VGfuF6BamfxMnfYQxcotNTvkOfmY7GfJ94kEc5MpSxEu0vrrW7ymQtRNXpPAIeYu3jcJOfyGOfqY7GfI94srOoicoiDfufFmVLhCfdJY3vStyTdN4Hv6P6gZ3SMzFj/2dhPxf1mI+bQctulFPE1f0VLSLMWhl0tlmReiLiyWrC4o8PEGLSSuOfQyh/KuKOiSyavZuWeXv60M3i+tIyaI/85Bat/ISGK2qIkeM92Ry5hyTwalIxNpIt9Ctw28bhUrf/tFif2hY+6r0bhyCTmZ1hmBzvybsZgQEvg4G/vRpDd7hXBaDpHHd6V0WVrvMqTsP+FGDFhf378H9wd+aXyfBlHSqC7yj0C3PSlrv5ehW7B/qjP3C9AmsghpwtC3zXVBUK4Y5xpdte8r3CXwLcthEQ7VcmitL3LUTWVMzhhLL966Sr+aY5xZdtf8AL9wlMkh5MThMX8/TfMT7pLG96VuSUqziB7+j0iNXk4N9WxBn5hY/9nYT8X9Zj9Ifz0FyK9W5YaRqJfNB5NCpRoj9ItFNSlMsccr8arNb5RYyB3nJUKVw3zynsFv42jMPCFpUeUV6tyx0vh5jM3G4lvKoJo1U46kGGEQ7KGmyutoKhEI+Uwk0ub6ZJ25wag5TCSu0Mr3qyTV8nb3iLcegojLL+Id0/QMbxiOx3fIMfo8ZcZ3/AH0KRW5wip9IWg6Rx3eldFlzuTiKalYfwsxYX9+/B/cHfml8nwZR0pgu8o9Atz0pa7+XoVulOo9JUKMeIi7oc3Zj2a95Y5vTHs1/yxZh9yJkcM46s3FneqpX3jFs4t6ClbS2HVNLyxFVB8Rjm9MezX/LHN6Y9mv+WHJzHPIUhcW6tCsDI1adyQ9OoLvpC0T2Qkcar+WafHgLIfs7Cfi/rMfpD+eguRXqEHYyWwi71xT5l9adRM7RwMpRRThLcLQ03ifuDBRNs5yRu5rCNJFoQnZyhSm4VkzOjbSC8BEJzMjm0xdiOtPBBbC1bhKNOgzLkEhOslgu9JH6RFqRvC6Zlw/7RYxalWhYvGZ5qvRuHbCUJOhxeJfy1ewc+Un7M/61+wS2cwc2ym9Hcrk6Xs0y9ItB0jju9K6NBv75ZJXXa+jGZFpOnVhiwv77+D+4OfNr5AuWxbSDWuFeQktJqbMEVTHO/MexHBzvzHsRwSxtTUuhULK6pLSSMvALcdKmu/F6D3JPd5qwt+l3KFWo+Q/Z/MPkH2fzD5B9m/KGrmTLJXbmq7oD+SufHXLtev0D5B9m/KPkH2f8o+QfZ/MIum+XqaL5+kWVZN6ew1OtM1H4hbqLyUsQx1zq/MX+ELIfs7Cfi/qMfpC+egvuq9QpNI3D5W/X7yhLLExkWojiPkrXHirxCDg4WSwdxujTKcVKUfnMQM3gZ5lmWzJ4k8JC06SFqbKsswyoyDTk7nDbLRTbuyDpJA96SLewURGbxyDDj12/XJoNVNAsnBREHaGGy7DjN5K6ZRBprhuPfPL+8YhIF+PdycO2bq6VoQsNLIqXb93yypm/cpe16RaHCRx3ej6NBRW9Xa9aekJMllUsSPoszjd8uXU/Np8/Vslm6pPEmu7fQrBSQm2MAoscqniNImtpoKLlz7LZrvrTQs0IO6tJ7DHPnAbHvJ94584DY95PvC7awJFgh5R/dL2ieT1ycrTm5NlHBRu0FBQSC00FLpSww8a8oitaJ7oxamfwk2l6GmDUaycJWKaajFBTds7MGpXMsu9W4STLNITybLnMabxldQWahOwhZufQEFJYdl6IJDib1Sof0jFspnDTJyFOGdJ24Sq0Crdy9JYIfV+EvaIq35/u8JTunFeoTGcRc1P5Q8ak6kFgkvALNTRuUTLLO3rhoNJ3RFWzlz8M81dez0Gng+/ds/bApbCpholtS0I4K0aSHPxLPpO+QI61sCuaQESjKrQyTl6iccaDn/l/1MT5KfaHDvuKPaYszNWpNMTfeStSLhpzNI5/4D6mJ8lPtE+tgubMHDMtZFk+EZnnK6CyRKViHiIl4DIquXvgQEfvY7i8W/QEmS01I6kfQtAmUyytWmjzNatvV9Pg0+DQUFBQUFPgUFNynQadFaaylcQorqjIUGXO5dDN2/nB+7ezRkDuXtyEjlwh4Yo+iYho1qKLNOivon0CIimoVNVqpxaxGzJcVmlmN7OgKO6VQtal8gIjINOVwPtAJtRprQJWaNBiuNQ68S0UIMtE5WoWm6oyBoMixIZZRIu7ugMTZ9nAzyhd0G520rhpUjzhMxhlf6peEb8Y+ub8oKmEOn/WT4A5OmE8ElLD85ecwRRsuLSFKNR1M6nx9BcKouC4LnaAl+6ilA3Q14h8kkeASypSagjNOgaw4/fTSgYbJdahwrqzIupLguA007QGm8oFouKoKHQJeNKaBul/O0B80nSgQwa01GjqUl0GU4gZ17QCUadAM6mFvkbdAy2ThnUOouLoLh0rTAJdUkqEEGRLIzD60rpQNtKdOiSqGZcScXMeIgphkiqaEkQiVsaG0Y7dxSTQeJU7UE0vFXQHySVKaQhk1lUYpPcN+qKUDCSUrELaq5dQVeQMy7W6fgIElLacKJIPTBKMEZx7Q68t4846hmBW7ieanjDMMhjgljtMRTjJJo5nHsLSD7UUOmgqECPOqYfUkyKmkNs5QgzLj0uHTiINtpaKiSoHo5DeCc9QdiFvcI8NgZg3HuJO0wzCNs6rytph19DJZx+APR63ME5iQhtTx0SVTDEuJOLh14iEXDb3X3J6O1OXNmdVag9GNs90rYQei1vaToWwgzDLe0FhtDMChrE89QcdS0VVHQPzE1YN5pbQRKdVhVRhmW63D8BBKUtpwIkkHpglGCM8w48t48469qe+V5MkFmp4g0wt480gzAIRivPPzBS0tpqZ3SD0x1N+MxVTqtalBmXGeLmaWwNtpaKiSoHo5DWCc9QeiVv8I8NgYhHHuJO0wzBts90raYmDdyIr9LHtRSk1qIi0mGZeScXMeIhmtp1JSHpiRYNlXjMLWp1WJmZhmXqXivMINMIZLNLwh6MbZ7pWwg9Fre4i2EGYZb/BLDaGIFDWJ56g46loqqOgemJqwbzS2gzNR1PHtRhf1hAemCUYIzj2hx5bx5x1DMCt3E81PGGYZDPBLHaYeikM6Tqewg9GrdwLNTxBplbx5pVDMvQjFeeYUpLacTJJB6Zami8JgzU6rWowxLjVi5mlsEa0lqFokqY9qTMIt7iTtMMwjbPdK2mHX0M8I/AHo9bmCcwghtTqs0qmGZcRYuY8RDNbTqSkPTEiwbKvGYW4p1VVHeMMS9bmK8wg0whkswvCHoxtnulbCD8UuI04J2dqSIlGQStR0wD0wUrBvNLaCJTqsKqMMy7W4fgIJSltOBEkg9MEowRnGHHlvHnHUMQK3cTzU8YZhkM8Esdph6JQzpOp7CD0ct3As1PEGmVvHmlUPN5Fd2tT7UqVwDMuM8XM3iCG0tFRJUD0chrAs9QdiVvcI8NgZhHHtVE7TDMG2z3Stph19DJZx+APzBbmCMwghtTqs0jUYZlxFi4deIg4tMO0Z0oRaCBneMzPX2pEdDqQTMU5Kp8PYHotb2uidhBmGW/wSw2hiBQ1ieeoOOpaKqjoH5iasG80toIlOq1qUGJbrdPwEEpS2nAiSQfmCEYIzzDr63jzjr2qwSGVnn8PYegKUltOJkkg9MdTfjMGanVa1GGZcasXM0tgbaS0VElQPRyGsCz1B6JW/wAI8NnawpZr0mZhlKFKz1XSDBw7ZZii5Q7GNta7x7CD0W49ronYX+yT/8QALBAAAwABAgMGBwEBAQAAAAAAAAERITFBUWGBEHGRocHwIDBAYLHR8eFQgP/aAAgBAQABPyH/AM2TCVyMl3FZfYy0NXQhqzO9H+QB6zfkJZzf9GUeSlGtQnAzXEaNn/wf6oEMpEIQhCEGSJzKndO6/wCdCDwFRxfsdqlYZpGiG1LSHnSJwSCTBqZMJ0be+R5d8cvrtP8AIuJCEIQhCEIQiaOQ3+YmwNWchojCsPpw4lWySscrl+TVRfsaXDXR4VCpmlVrsmdBK1qcaZVfvmLBJne4MpJVomgSwj2ZePFFWWhPobow7n7kLPsaCbST3l/ALJan1b3ZglxcE+EKagi8HC1G4nHI9cuoaHlF8F3GrjZlKrG1ZGjDw+8+nyvMczAhCEIQhCEIQZTPVS2fysU1fF/HPVfOvZS9l7ELxbxSBu6/wY9SLv4uPPqvwbe+DQ83370L1ceHF+hNwMJeRcHmne94fFzNLbk5Fj0zbOe/isixN9P1+Gjbp6QvDp6eCMTdJOaXDvZWncITk/sTzJr0+m9LZvNHnz98maobtvv3g75dc6Xj3InEvFrPpTFJZbwhaXr3kIQhCEIQhCEJ8pbPi+sX82lKUpSiy4KZk9hbsufA9++Pf2UpC1feJcledkybR18ME9J09nkL3PfvTsjieBc7+vvmYJbFpdHdxZa3Or0RrWuYVLuXsceU8i+opqrdG0jfPr+ylKUvz7D6LepCEIQhCEIQhCE+sAUSdITSWvDA05zdOrmeBkUy1LD3fP4XHrroQzmIJsSBvLTQUUnwpU9h+niOafVkknNRDGEWrGP+ASu9aY3NeMn4ovHlUfcNs9NKlJJ+S3RbzeiNMDmhMZ2x3M8mW8fVHA2YDapSb1xEqeGr8Y6UxyZuWYs7Jr/AuMV0Ha1NRTDePjYYF4rl5jbrdnqJKw94g0TUnG/QXmxi+ZsX9FypmJVxO6s3sVLKrXnqw1+xaGk27XmfNsGhCEIQhCEIQhCfKV/FWB/LY2e3eo/QQ5sp7CN9qjTG5j3n7Hvv2PbfsK+pK7wFZKfmgsYkMhXqR7f2KsLKuLj1j4EfnbGNazUoPeWqWx5hq82/12GI7zyYhwLFCRfvRft2YovchnJBieo0ZuFz0HVsvKYNrtd1vUS+pFJ2zj3GADm/+obCZwsPdXqc7/QG/DYDYUa0ePeL5yQl1b7xajRIYddvFatWebF7WcaVa9hC79EqPduM/wBIVH7CUrGg8L/C7F8vxCIQhCEIQhCEIQnyoOP4v1cWny2MS6txC1jsnRnnkhR37opErY3b+CTtf5BXHCdL2VNjaIYaePezfgjPpPmSj+gVlnTNWdjKWVCpu2/oXwRDeKyinEgGXWN+8QTcx92hkNHb23XATsHo25yGZrbZd4vS3Xyi+1VTbCSjfYKlyVr0HSLBlsintjGJ6Ubsh0XzSuWxrtnW81lxNBdaaVHvH0Fqu2mrJ24/B5VuqmhH4ReVNNXzME5MS76JtdeLF8xPcbEIQhCEIQhCEIT5eneUaWIamNOsL5jN76TPe3jggkpN0T7hdrPYuHtlXsOVxfGyMt8eZ/YhL1tNFr+oVidNeEQf0AoOT4wS8+Z7lw7PeeDsp1Hvi7pM/vGn4YKMtl3I/Yr9OiTfoqiy/vJSVdf1DJyyPOUdcXwHHncF0SejSKvSVz0uoXeqf0rTsdF4kv8Amj276HOCxJS56LkhSz4vkIXy7PuEIQhCEIQhCEIT6pj7Xea/gXaz2Lh+EMcnF2KL37z+/CAdtIsekK232Ko2+h/XBb6NHF1vM9m4dntPAL0QlX2KsJgcseGuiMj0twPGUF7+LG69iRdQSWPSW9iB0E/K9qajeN9Dn/49p4+xiNN5ef8AT+n/AKbTPzJZfFns3AQhfK4yeU/bIQhCEIQhCEIQn1T+ArzX8C7We1cPwhjk+rGB9D+fFn+oK8t6htCrpidSa9WfxgceydPM06Hs3DsZBxPKfwvm7Ffd+PYb86b5dfSu9ifSuW979NRydGXvB48ivUU+ZPcLH5Pw7ELUo7a0dx3kMML7JG4R5GpZdHyRsEPNuyWve+xvi3xD2h6HBftaHs3AXYvlT2/a2IQhCEIQhCEIQn1THv2q81/Au1nsXD2BoQX1giSfA/8AZH9YZ+3hW4j0HRMzI5p+R/eH9YK7RTtJ2K3D2sVQl9m7+zX2fiER9PY3gkkxvX6ebP2N699KXNx/Qwuzb0SQ61TKNn2fe+x3XvFoObbrzN9xqFdrGJ11Xz9j7JgfZhPASDtmhcGezcBfMTjqwxezMLzIQhCEIQhCEIIbB9W+0ytqWtilV1IS6wYhLLeD+Afzhjxt2zS1GjLh+JOy7/B/PxxfBDG5Vjl1+Cbpoulmko2TdwkwpW90us/PsD+y8Q8nHa8hGPe1vds6k3cdI+IcnsCtrHqjeFmucDhCEPcuB1Z7BjZ3HLjxBnKuxfabnfC14fjbMz/I6o9qEIXy2brEvISXxVNEIQhCEIQhDRMyXkxN3H6tjECn506ruLWn2cMdpZBAtpohn8aP50U/D0AlnrlK7xbIQYYYRVbkrLPUbPm7SPr2kILHEmG2yIre2asyA/OKs4cGK4mFNSziZh/JX5EbSuCryfsrVJgDSWiWvMfoV4aku6gnZszysODTFwQz8cWOFFK+XZtSj0Y14jcylVOm1xa4DniweNY5Q25IRPjjek/HpsGNfgfkGlMWyk0QhCEIQhDQ28I6Il5Fy+rYxoa7EIQnYhCE+IKEITtJ2IQhCEITsQhCE+RE2CQ1xsNlqj8JTuDanqUE/wASdm5VroHfhMD/ANIQhCEIRDbZct0K/cfV9/yEtYcvM4EMam0x/H8fo4QhCEIQhCEIQhCEIQhCEIQhCEIQhCEIQhPiiuBSwC1Wc0lB38CBmeOBmywVmxIWQY7U21WGYhcHV4mtB4BThfg/J/Gmp9RfwXbPJRF9HVPEMbztW1fyZUu3UO/SQhCEIT4YQhCfDCEIQhPghCEJ8pmpTRiq7RKxu6RERo6aLqFR5M3tmwlgq+jSr7eX7AudcSLdUaqjnEj/AEY8QdYbvDIaXAy3B/Su5h8IY1f2A5rwZR5ZDVlrwOEQQ5Moa7tzDWzBnRQ81ZuUrczTfQGNKbvAzWu9i8BKuLLJxzc19od9Qqw9wd0wjK2aG66zj9IN3LoZFcuXZikkR6F5PlChfybHjTahdqdcPHuB0Irmn2i3aAprlVyYOvkHtdmyGXg+oRrXdl/oGg/8E0MykVNaBXWfxFz1QvQUar0hmHOs+07HpwlzLipGL8DhvpcXQld4PQh2vDcpI5jUjUZ69kqKUehfTzdizfwrYk+0nS4bbiAvz2JvcIkkkvUwnkaOrJbOQ1I1qXugaDLwTQyqRUlwJTE2faJadwIzxfSLs10RZqdIrnI/2xJCfmLCpGPbO8yHQ82TQl2vDcvK5nUYGNt2/tF0nNxXVlpPlixfybHjRajczrj1OYvs5Qin8T2PCV2LpRNaAq1tnqyMzkdRUTWe9/aWXSIzsCT6b4NS50XUhmFLZfSOTXRFur0imAf6Ak1F+IsqgYzkF+0kEmtBcRzGpEIz17NUEo9C0nn7Fy/kIfeQ3d64XvzmLfcWoi38T2LuO1a0+0kzpMvZGTI4dSdal7omg78E0M6hWYgFFN7bih6iSzATavSFNTSHEflDav7SZYjWgiiW9LTcxbBMvEdDzJNCHalJHMakakx1OACilEo947FH3bZfauWLsJVKJrphZS2erM0chqQ7XieXJoMvBNPtiFjNKRXN5SCt4nkWjbqf/iRX/8QAMhEAAgECAwQJBAIDAQAAAAAAAQIDAAQREjEFEBMzFBUgISIwMkBgI0FCUjRQJFFxgP/aAAgBAgEBCAH/AM2aUZR9uIxrE7sTQdhQl/2CDp8UZ8tEk69sHCkkx1/ri4FZzQ+1D7UDhhSudGBB9874dw8uOT7Hyy2FY7sTQbH3HEFcQ1xDQdTuZ/sP+9j/AKCQaVswokCuL/ritQl/2GDae2dso85H7sPKY4dpTj7XQYksT2Qx08kY49pHx19qzZj7g69oa+1c4n2ynMPaSnAf1lvbPc45G2bOqk1FazTjMnV9zXV9zXV9zXV9zXV9zToY2KNVjaJdkq0uyVjjLKUBGK7hVvspZYlc31ktplC4KlB1NOv3FtszpEQkq8suiZRuRQzBT1LVxAtvIYiy4b4tk+HGrq36M+SoY+K4Sm2Y4GNW0HSHyU2zMilqt4DO+QS7PaJC/mynxe5Pce0vefN2W2E+G61u0tA0bdaxV1rFXWsVC8QjGnv44xjU+EsjPWQVZ2U1u3FWW6lRuGY9lzUdkT11TcV1RPQNygwF6J2kDysmaoLVZpFSuqUwwq3hFvGIxd2S3ZGNsttwlBwtRXSIaZ7Vzi2NpTR9/ckDOwUYYU0sRn8VzJFmjMdWAy3LLU3Latlr9QtV+cLc+a/qPuXXHtIuHmAY0mz7mM5ll6dAMWJxOJ3wQ4xKa2hHkiB3x+gVdyrFICx2ofxt5TNEHN3ObZM461erO5NyDjdqDA+NRl1cZM1/UEk7xhqup54iKso+JmNXMWSImlsHdQ1dDYScKmsHUFt1g6RylnuNoIBliq1i40oWuBLVtZtDJxHZcykVHYvDjw7yK4VPH5jeo+6ZMaykbgpNKmHmjuNWl30rNUi5kKnsW3JStqckb4/QKurPpLBq6qNW8XAjEdXVv0lMldVGrS16MDV1yH3W/OTdZcgVe23GTMNmLhGTW0mwhAqEYRqK1vqn7om3W2XjLm/x6/xq8HTVyVJfzk93TbitnzSTZs91yH81/Uf6zZP502h7NtyUranJG+P0Cr26kt2ATrOerWQzRB2vJ2t4wydZT1Y3D3AbPdch91vzk3WH8daknji9dndQwxZWW7gkIUVa/UuJZKvWy27b7eJDCtbSQJky7P8A5A3dGt66Nb1HHHHy7nkv5sw7/wCs2T+dNoezbchK2pyRvj9Aqe1juCC/VsNRRiJAizQLcLlfqyGoLZLfHJdch91vzkpzghNWH8da2pqm7Z1uec11NwIi1WEeSHGtpydyx7rCNJZSHVQgyiSGOb18GOG4jyUyPjWR62YpXPjc8l/NlXFf6zZP502h7NtyUranJG8XUwrpU9dKnqyYvApbaDtHECnSp66VPRuJmGBq156VdHLA5qw/jrW1NUqOwgQ41LdxQClz383i7kFXEvHkL7hVtyUrahIyVYEm4G7p1vXTreop45sclzyX851yn+r2T+dHSjBKoxNdDuK6HcVACsSg7U5I3W2HGXH6NfRr6NLhh4Wy4eL6NfRr6NP6zViua4WtpvliCVYfx1ramqV9eSodnSv60jjt0wEc8Vziq3tkqrxI91tyEraUbvkyWSOlwuem1NIjSHBNnRPFmz3XIfznTOK082JMoxPvLS5Ns+NDacBq4v4ZImRR3GutIK6zgo7Uhq6u2uT2rW9iihCNe3cU8YVOxZzLBLna5uDcPmqzuoY4FVtoTRylcnWcNPtT9JbiSb12cwgkzM+0YGUrvtb/AIS5H6xgqS+i4qOOs4aPeatJlgkzt1nDVzfmdci+Raojvg90qJJ4OiyZM/Ykjzd408uKLDvPwq3teMCxdDGxWipGou34eSrXh8T6l3w8/gFo5jz7nQPTIU8hULaJEE8jSscdwb4AtvIyZxHK8XoDeLMbm5SWPKtrbrMCWlj4blaMToMWFzIEydgwqaMDfbhvWRqEb0IGpYFGvkvvx+AR3jJHkq3CNIM94sakZEtZHTOFdk0B78anuxKmUWkKS45pkCSFV9k4xG9RifgFtb8cnGaLgvlrIwGao7t40yCApxBxLwxkjJFaNKmeu9T7UoDXDoDD4AkjRnFSxY5mlu0eLILWBZmOa4iEUmUGFwuekuJI1yrCyrIC95KkmGQKW0SED1ZUpyn41hh8Qiy5xnvFiAGSG1eZcR4o2rWnvc0WSrONJX8dxEqSZUSD9gAKaYD0li2qRFqVAukjJ9/iMVy8IwVGHEzPePEyjJBamZc1LCce8KF0aULozltUjZqSNVpnC6tMToAW0SH9pEyH4nblgpFNKq00jNSoWpYlWiwXVpidBixpIP2GAppgPSWLa/Es5wy0qltEhA1JC003697GlhP5BQujShaZy2qxs1JGq1MMG+IgY0sIHq7gKaf9SS1JCTqqhdGkVaaRmpULUsQWiwXVpifT8Rj9QppgPSWLapEWpUC6M6rTSlqVS2iQgerECmn/AF72NLCfylUKnd8RWNmpY1WmcLq0xOgBbRYf27gKab9SS2qwk6qoXRpFWncv8SDjLiWmJ9IBakg/YACmmA9JYtqkRalQLozhaaUtSqW0Zcpw+JrAfuFC6NKFpnLapGzUkarTMF1aYnQAtokH7EhBWvxMTDCmkZqVC2iRBaLBdWmP272NJB+wAWmmA9JYtr8UiCHUkLTTfr3saWE/kFC6NKFpnLfGMSaUA+pcg9LSqtNIzf8AiT//xAAvEQACAQIFAQcDBAMAAAAAAAAAARECECAhMUFRYVBgobHB0fADgZESMHCQgOHx/9oACAECAQk/Af6Zte6z/Y/6a/xLsbClFPkU+RT5FPkU+RqrOGiqWsNUSOZwVxPQqmbOJPqeH+x4K9eg5NyocFenQZVp2zvZblLKWUsTEze2clEzZofz8EFCy6lMWyKjYcQRP2P0+BWvyOl/g/T4W3s1kNa347bcMeWHhHPvhpNxFIoi+pIyq71GOzjIzs4PqvwHJufUj7FUrtmmIxcI598Dgr8DYcFfgOZOLcq3XzNVbc4Rsji2hHgR4GkWcFQ507a6euLhHPvhg1tBscW5VvmY4GOcfW9KKUI47Z6euLhHPvg2JNFaTc4tz62+ZnW32NTe6nKykUa2Qjocds9PXFwjn3vWytlbPmY4zK2Vsqtzb5mdTMeZojTBwdb1eZV5jmDjtnp62pdqCg4Rz73ggi8EEX3PmZ1JZkjJGZtfgUijW6kUfGcds5p23tJImZJYtTnFoVZjkTKRmhOd1lbaRO6fz7ihfv6912bd3FkPCsaELuejS7iytp3WelllbQ1H3YdlmbWWQzTCu6WhrbJ2Wfd3Ucd3tP6k9e7+v+Q//8QANxEAAQMDAQQIBAYCAwEAAAAAAgEDBAAFESEGEhMxEBQVFiIwMjQgQEFgIyQzNUNQQlFSYYBx/9oACAEDAQEIAf8AzZlE5rIT/FXjWt5V6EJUpHjShk/8hJC5f2Ka8taz/Qm6gaUpKXP40JR5NvoWhf1yD9SREGk+lD9KT6Z3E0rVOfzrruNE8th/HhLywbzzQRToURWiDHL5ZMryRr/aANbg0of65UI45/8A34OfPVaVMckRV5IKVupWP9YVPl3nNxPOjPZ8JeS2OazWazWazRpj5UR3qxjRPg+ufJ5/EqfX5XlRnvln5gdErNZrNZrNLr8qI7unx4+nkc9Pj5L8pJLdDHzOazWazWazWfNmTmoKIrrd8iOGgJIuEaIW4921ArtqBXbUCu2oFJerfnVp0HgQwq73F22iJhH2lcddFsgeyuDx0J/1N2iciyDZCz3Q7lvqZOk4vh3THWmXN/RZ+0HUZJR6tV07TEuh41abIh72VCnnNYR9GzRxOl3andNRq13DtJlXamSOqME/Te1DBkg1c5/ZzHGpnajiui3VxnjbmeMUPaFqY+LA+ZKLJ4+azWazWazSebfw3oea5VPtbtyMHw7tyK7tyK7tyKO0vASjTFlffLdSGKx44MlxKul1hzm+rlHtkd9vjNnfIgpgk2mhLz7zQa7ywaULe4SkVn6mEcmYqCoJUmRJiMm8Q7SOiW9U2Us2QT5Wy7nbEJBn9odZMkTtEtE7PmUEe5NpgOFdUoCPdSnXlZbJ01XK5pqLKCAnCtUeVwpIyKv58S1gdQ/ctVtUeIwBWzwb9xDzXly4tZrPwZ6M9GejNZrPllWazWazWaFPM5Jmnb1AfHcdi9kTF3GxFBRBHpmS92S4NWKRxZJJ0v8A6x1aorkqOogOzArqdwipCkkwlpgDcX1aLuuzV3to21QQbUShOa3akA0bRI/w7CtTo8BmQQJarfAlIeb1ISPw0q1y+NLEaevzLLhNKl3aWMsqm7+w4aAlbQR3pMUQZt2zjxGhy0TFXWV1OITtdfi1dL01NjJGZZc4LguVJvzMxU6xZZdtcewz5jv6hfNKGawqdCIq0IY8xKVMpirlbOztymDVt0TT4J/u3a2a92XS/wDrHVqvCW5sm170hU+Uk2SUhLVPS3PK6vekKu1zS5KCpbPetdE/2b3RespOOrJc1gvcMtqHMyQbrZgFOYp1MPfkulS+CxpUBFKW0nRc9/qbnCzckpO015fj9iO9YqLs7AAEU+wrdW0kGPC4XAtOk5nzZGjq/wBUlJW038NN+tPhn+7drZr3ZdL/AOsdWS1sXBsye7tQqukYIcs2W7LBanyFbd7tQqvduZt5AjNs9610XD2b1ImVxV+/cXaiwZMvKx7zapsyVxWnLRcIwK6dXUerW6LGqxt8W4N9NylvBMdEdl3ze42/tF+3H0JdLkldqXOpUqTJx1m1e+Z82aODQvgz05rNZ6M/Pp0bS/w0360+BKn+7drZr3ZdL/6x1Buj9vFRZ7yzalSTmPK+5CnOwHFdZ7yzanXF64KKvWz3rXRcNIb1Rx33gGr9+4u1sp6HujaS5CqdTatMFZ0oQraGTx5yimysfJuSFq/ynokUTYccJ41cOLOkQs9X69Im26R1im5DO4ldZZratwHODuWr3zPmyw328/1SdG038NN+tPhn+7drZr3ZdPZsNVyvZkKuzIVXloGZzgN7PMNSJRC92ZCrsyFQW+I2SGFXT2LtWlviTmkq/fuLtbKeh6n9oZz6YqHaJc5co7wNnoWG0Q3jwluiJBjCz0YQud0TE16tkxRePnaEUG3HiksFyVM13fuVS7fJg7vWLV75nzpDXBPd/qkraX+Gg9SUMyMa7ocq7VhV2rCqYSOSXDHZr3ZdFw3uqObv5uvzlfnKPf3vxGuJn8L85X5yvzlMfpDm9uI3AcrZljflK9V+/cXK2U9DtfkI2tTNo4rCYZfkSLi9vHJgS7buunZL2646kaT0XT3z1bMSGY/G419ksyLc5wab9CU/IaijvvbSzI8vg8C1azmfOkMccMUqKK4XzIcbgjvF82lXGAM9rcotn5iLpBs0piSDpkmRVK7vTK7vTKHZyWq6222BbhX4rpZpUuWbzVktUmDIVx74LtEcmxuC1bYA29jhJd7XMkTTda2ehvwxc447MzF5sbLJ/NEt8aEn4N4hHPjcJtjZ2a06J9N1sCzHVfY7tz6jWKWMV5gu602gTdFEq8QXLhG4LfdabVrsAQXOO75BqqJo2qqmu+mcfBKi8bxCqKK4XyocPc/Ec/s8/Lke7SLlOjhpnNHnGjeca8RM46H4wPpq9GcY9XxssOPr4I0MGNV+NEytCIjWi0YY1T+/3kRcUoovOgBUWjPdoVymaQkWtxM5+B2Ay5yO2uJ6ViPjXV3qSI+tBbXi9TVuaD1IiCmE8gNK3q3q3vsBW8rmizjRvK81NEXFYRegG91c04SjyFcp8pvVvUi5+wDLdoV3krNK2irmizjRvP1VxEXHyyjmuHSafYCoi0lCCoWaMt2gXeSt5M4pQRVzRapo2KpzIxDUnJar6EccVdGRd/zpCQuX2evLRve+pGg1z6EbwuaNVRNEPA5NyX/wVVNdW4pF6wbFtPC5JAOTjxuepgHc5D7RUEKvpo2hJzI92jlp/gRka+JuMR822hb5OPg3Tj5uU20TnpbiiOpEYgniclqugMO8UftOWf8AjTcc3KbYBujeBvm5IM+QARr4W4iJqeRBKcl/QFVTXVuKResGxbTw/aSNDvb6m4LfNyURekRU18LcT6mm6CU5LRNAIyNfE3GM+bbQN+lx8G6cfNyopbzf2iqoKZVyUq+jU1puIq6mIiCaOShHQTcJz1NxzcptgG6N4G/U5JM+QARr4W4iJqaYT7Re/TWm4pFqQALaeFySIcjdNz1Nsm5ybjgFG4LfqclEXpQVNdG4n1NN0EpyWiaBHMjdyX2i4+DdOPm5QNk5ybiiPqIhBNXJaroGpLTcRV1MREE8LkoR0E3Cc9Tcc3KaZFrl9oq0W+oi3FRPXkQSnJf0BVU11bikXqBsW08LkkA5OPG56m2Tc5NxgDmbgtp4mz4g732lypyWiaARka+JuMR822gb9Lj4N04+blA2TnpbiiOpKQgmrktV0ABV08UiYTH2lzpYhb2jbAN0bwN83JJnyECNdG4iJqeRBKcl/QFJTXVuKReoGxb9P2pIJ0fSiKa6NxPqaboJTktE0AjI18TcYz5tsg3y+10FB5GRInhc4x+tuOZ02wDf/iT/xAA+EQABAgMCCwYEBQIHAAAAAAABABECECEDMRJBUVJhkaGxwdHwIFBgcYHhMGKisiIyQIDxBOITQnKCksLS/9oACAEDAQk/Af25HvUdxX983LEss6eEz2vWWiXTSvKu/WX96daezfLrR2ev4X8qu7s9fyuvLwTeuusnb66y9rrl2+uX6XH3Y9U9VEx9VabDyVpsPJWmw8labDyVpsPJFwescoXBUAAMzKAEQqFhCyulerJ20+uRQYLNjfgJAOFYfV/agz9ixu+b+1Q4LFsqDsrMh1C9fJWN5zvZB1ZkP3ziMogBgjiVGOvRRjaowohTrIogqkAIJwx81bMBlDIuRkHNA9eqwuvVYWoK2ic/KP8A0o8In0QcoOBzVmEGf+FC7rDAJ0ssP6lYxf8AEqCMDyiUNp9SCAYShirWjqGIPBjfjLHg7is4b1jO4e6xPu75hJCgDq4djKd6ycRPKVlVrsRdm3OizB1aFRO6yy/LjTbVBkxlWbt5rTKG4oUBZQ3yhc4XNUhGLGZB/df0sOuLmoGAbYsRdf0zt8ysv8O0Pr3zG7via5Yj2c6LeVm8RPKVA7lWW1BnbcyhdwystqhZllEs2LcZfL9oQ/DGzrEN6xBY4jvX+aPrcs4b5fmbFesP6lh/UnfCx/7csocI6fZWW9Qs78FlHfPzcO1nRbys3iJ5SnoiVcG3K4B9oT609XWUSzYtxlo+0KF2UDhhjCgYDSOcr7+taxV1TOMo3YPFaN8rSJWkSiJbKs4fFx92fNw7WdFvKzeInlKaqbV7q8q8hlg6vdNRZRLNi3LGQtH2haJH/VyX5RU+Xuroac1ipKJjhcCi5KiZ1E7YG8yjGtRjWi/5uCzh8XF3Z83DtZ0W8rN4idkNSshqVkNSDANuChcYPEKxGpWI1KyAI0SyLLuqtH2haEcHyULDKeqqsZ2nkFUlX4/OeUoZv/ZDJvlZbYeastsPNQs/lwWcPjXd1/NwlaAnzlahWoVxJ3rN4iV7FYW1YW1YW1X6U76FhbVhbVhbVkCx81dCN/RWj7QtCwIdQX4zsVYj1RfhfIUXe4zzioxC+DeWyqMRM1xfHOJgo3bC4LKPjX/GvP60sRcmPqgGGmTa/ZNr9kQOvJF4j2gGLblc3Edm91U41ZuC24KFnbijDt5K01Dj7KFjtV7oihGOcTE5UBrTAxYOPI6ih28pEAvj9VHDt5KLCiGofHv+Jf4TvQp8AKp8LUKLqFQHUoVRV/YoUfDdSqSqZU8MUEr1cgqmd/hSgnQSqqCVSqeFKmVFWVBOgleqBYvCdTOsqBXyoEFXwnSVTOplRVMqDwperpVnUzqZUCv8JBVVJVVJVM6nwvQSvVBKvhe5XyoJVVBKpVPC1yrKgVZUHhoOgqK/9kn/xAApEAEAAgIBAwMEAwEBAQAAAAABABEQMSFBUXEgYYEwkdHwobHBQOHx/9oACAEBAAE/EPQ4cOHDHUY6wx1GMdRjHWGOGMdYYx1HWGMdZYxjrDGMdYYxjrDGOsOHWGMdYY6wx1hjqMYx9D6HWXDr0MdR9Thyx1GOWOoxjrDGOsMY6jrDGOo6wxjHWGMY6wxjHWGMdYYx1hjHWGOsMdYY6jHWHD6nUfousuGOsOoxwx1GMdRjHWGOsMY6wxjHUYxjHWGMdR1hjGOsMYx1hjHDGOsMY6wx1hjrDHUY69D6H6b6HDhjqMcMdRjhjHWGOsMY6wxjHWGMdZYxjrDGMdYYxjrDGOsMY4Yx1hjrDHWGOoxjgU9dAhdIQN813SzjvGiB1RBU9tBhrYXdNIEj+/8AvPPOjeHL6X0OGOox1hjqMcMY6jGOsMY6wxjqOsMY6yxglQK+xHHSKnR+Ykb8S2HfzA2yjGOsMYx1hjHWWOGMdYY6wx1hjqMdYpr+9XPhKPnq9Xz9AARZ+ghlzc7/AJD6DqPocuox1l1GOGMdYY6wxjrLGOsMY6jrFlohEQ2zX1pHl9iDOhG4dBx/tsqmDhjabnPx/dnP3YrtR4ivH1kA2p/twEG20ap8vyWR1hjGOssdZY6wxjrDHWGOsOGOpxu4v/N9oEKPp/8AnVY5F9x6XUY6j6Ggg7XRFX4g0S93bEtbstkZDNImmOsMdRjHUYx1GMdR1GMdYYxwqdqdFb0hq2tm03a2G65u0OBgkpb9wM4d86pAF+hCwlJV7DitnRh0GjUix3vVb56UxSxwKZu34017W+1XUl+jqwJVZC9zXxRodqj7iQVfZV+Gwz8lE7o/0gSfub9iA/a84b/HuctC6JC6v+QTosbdkqydgXacUa9+RBjjA22STvwIntcQihR2ORA9RQC5Vnjg+RgA5uWVJ0A9XjXC3rDGOsMY6wxjrDHWHLHUYzlzX/hCigADRr6v/wDaYXH9Ix1h1GNd0GzYS0tLS0tGTn67tMOGMdRjHUYxYsYvBhYwsMrSLFj7DH5do0qz116bDvOLy6FGqj/Y+YREbQr9HsSvI8N34Xn+FFWq7F2s/wAgUJcranC0bOO/Ar1a7SqlNoT4TOFD0619O/3EUijYOuHA9uBGVXwpfi7fcQVqb4H22/mbKw5tOhql4BfmWECEXC16H35Yjem+w94p97jhPaVQRFXdpv4XZBB3033cVfyD8S+F2lfcCFdo3dbpbfmgfMVV6hf3D+wHeanoLP5k6Xp1ikWLFiy4sXDHWGOsMdZdQg1QBtbldShaOvU/8v8A/uGMj4MfHHxxcjDHUYx1GMdRj2wwwwuMss2IFqgQ0w8w8xsbbu/HaLu7VBbbUNWmh0PJ1l+V8fv4u+8CJulCe7ZENiLOfNe/k35VigDYAWAe5BEpFPKSAA+Je6we6/txFcUdug56ew6NjcWdgIJVBXQO41qkf3+TXHWiX6rcojfuTpfd8GiUVwbU/E370XZRvff9JBDXXtx/HezTLfPjz/74ua2tBwOxWygL1rjrXQ0bY2cibbW+5d+wcErAfCwEOnp1eSGHAwxebY6wx1hjrDHUpRfjLa/1+P8Am/8A+4Y/QAMdRjHUYx1GMYfV7G4kGi0nMZNzwQUB1y0vfChEKN0cbs9cGyq9+Yqm9CPWbjxtVSSksRla2btp/HX34YDZl4ypzqmBbvp7yHHN0NQ6T3u13GFt3+ibUOIu3zj8TrcDSC8SF4w3Ur1bsp5JczVSmvTvIqox5HsBE91mgiKVfB32vDNJDuvm0uvUUBuvizFW29lR89VoP3YnO7UAFz0WurNRcYdsoigQ71uofKcsTfs+8pXszlvsOBfd5D2AlPhUKQpZa5bSftc9CXXcXl0gQAh34RVyHb1LSfPbjsP7Jc+91y9LvuWX1TqBXHhBn5f2HZlkdZY6w5Y6gtbRv2OP8frf/wDx9Vw5AWer5KcMdRjHUYx1GPE69PyiGR9BUrqMEFaOoCpobvLvdm5hr46hcE7/AEB5Rf0jZjGq0tKT737pNxLzGpqNHslTpy26b9Egb4559sTz3A1vibpDevrP9RBIuD2VAWdEpcgisL6ORVppidUBzA71E6Ts8LrhosonxuY06qg6OHvLT/e/VCi8JxElKNnlEbsioXycNeAIhxia5VYfS/f8se13WvEtVdk5yQKtDppFDUhvdHEOBOjhCa+xCC9jhB0WtY1vieMcJTbfHpn8R/mGSu/lgxZY6wx1hxVg3/Mr/wAP/wD30Oo5LoDPCeGPhPCFf2MMdRjhjHUcaWaVmuWEbdtwEp+yOwmxiuuB9wjY9mhyrGOFAb+5tAmB/YGDH7Psm8NzWhrlEUdDvgBX3Zyf0N0KWU2rnkLPjC5S6W4T2uDlsA0d3whGUwIsBeGBYV4R2GDcCeEFnX+jy9Ptq3aImWp3qbFTSgPmNsfvgEjH8F8CGtW5G/wlqMuD67p+/Ae9IXf8K/74AenFRjbrIqrKGpDl2oeNmB/LvV+St+zUChU8S03le1xZjeWx8uzaJSc3n8+ausHBwxjrDHWGOoTTr/B/1f8A/Xgp/cgXB4WVg7VZ0GMU3X7xwx1GMdRjHUY9ziDVb3TP3Zx7VQcR/LKWYUR16Cd8sY/Q9kv+Qt1xKEMmPWK1b01brjGdV1FDhhMPFL/1bjjqruP2nfLwJQRGVyNY2EnSsqFCqLdtRXG5b8t/oOB6g/3vj9yRr/rYsERc4/wsCn48vgXhMVlFC6zV8TzBXIZGMvGfUhpuo6/5fRpwwVA1i0Lsnfisc1/8W/8ANyGew+cJI/QY6wx1hjqAX0/gU/z/AIf/APuGPqY6w6jHDHUY4Yx16NtJ+17sKYkGU7J1GfteyWsf4bK9BGXh4eOfjyhXoYTx9r06Cc2YSVb3ji4W3a7T9h3xcOWypyexhRm0wLFDY7kOvJXd5G0ckt1S/OvcQgAUQeX9Pbm58ND+VuaR92Dqd/SxlMCp/wBbljepSjux/wDKx79p78ydQ/bd/qDrDHWGOoKPN/x/6H/A/wD+6+g4Y6jhjrLqMY6jGOserDbwh+17sOPTzsnUZ+17JWwaw0t82oy8vXIpvcWgdXHPuJ135R4qRGVDN+Xdwqh3Z+w746gmbT4Q7OX65/dWMecpYkTT15NXjq+5Akyp0sTXibhIfWKKvhTif+sjYbd5cMFE6wAOQdOA3RIpxg5tjH8PT7RuVQd8v/TrExiC4iCW9sBlr528emyfuu/0jHWGOsOBf0V70f8AoV8/8v8A/wB1H1MdRjrDHUY4Yx16M28Ifte7C2LH6E7IwLxh2A4A5xfYHyx7/Xa17AI0Jcrt2x0sQnZM0pxeE2JcSG5WtftdjEB+c4cbBctZdlkxff5wGWSVIWminAg+lw8l7d109TvVC96Mf4MASo9+74480W9yUx+5lWEnx0jN3RFq1gz27eanXjm9xWCYh1THX3Gz95qvpT9t3wYMOWOsOEJEERNkWqU1Oh/O/rf/AOrJHa16XDhw4Y6jHDHUYx1GMdRh3BubyEgqEDa0ynjwn91AICaoAbVY/wDhPzH/AMh+Zt4lmYT3EnmHDh8fnqXy4qVPUddsPOryjfQ3fzPZU9utVpdXPHBqj/ExYVB4W4dv9SiU2xPd/wBbjNzghr14qvvudWecBewNeTIuFCWHUrq1+A0AQLhj6TARp0+LLCB5Z5l68erZNJgx+27Z/gzkUtXae+pxF0V1ZeP2zqiElaM4aF/klQ6aYdm8Vi+qK/lQQ+gY6wx1hjqMPO64+vce5CvkJWI/U5+gxQDatBKVfGXhv4R1HDl9bqMdYY6wx1GMdRhhu5TUcNyDYneCt73eT7hH61Nxdm2Em3Cuw4VM9K+iFR8rAXXILy3WLaPjF9Grgm9txTT4MFdMsK7nkwggljRvpVjgCyMzDS2mrz7irOXbzrRtI5BgauHbVXQ7Tx1MJe+u3/kSTt2VHxBRfu2+8SXS0vYOFOphM4812+DCRy/IpNbugQdN6m4+1VAhB6WVkZt3zi2QaHTsFMA7av4Tg1V3Q7v++W2zziYrOAF2zlYfQY6wzjqlWF2ynMBBQ6ZeqOPC+UjqMdS/ivlS7PbuQ1yCdifSACisAtVoCGXDTa/Y8+PU+pwx1GOsMdYcMY6wN5twssssccrLDKcfD0Gyww4n0e4WWXLc6iBPfDHWL1tK4NxwhXsh4qDyWbgfEa92ogeHD2LjZaa9m5Ryap1pinO5bn5HZgcPaunx2e5f0ADBwL+0Mcv+4X5f+Ry4YzSiS2n0moJ7gaFI9bfTreXUY6jHWGOoxjqMY6wkYYZZcDjMTDDLLjcxhhhlisfRjDkMOBh9SY6ixSffc55X3FrywK+sLMNHkqDGBBUATS9xMr0puVwTW65DDDCotiKQAA6P1G/NwsOu0Afkp/iWb55/UTqfs+Ycrf09UPHQrbfLz/EBSf6l/gMfXd2h8vpcOox9sTxxcxsRwx1HDljqMY6jGOoxJUYcDDDFJVdJUqJhSMssJEiErBhhwMMJKiEYcDgQiRiSo6wx1KYCFI6IKsuziuRyNRmZWwXliwrppgwbtjL2RBVdRZpVKLKdC0el9L63WBqZTAbX4w5dRjhjqMY6jGOoxxRKiRIkSVEjrDGISiIRIxjqISpUQiRMMY6yhEJRKwhhjHWGOoN9Ka5WMnQaYYZPSJXATSGoNaNuZcVdaaQu6JuW+l0cMRHmOH1v0h6aM4+ElyLy6w6jHDHUYx1hjrDHWGMdYYxjrDGOssYx1hjGOsMY6jrDGOssdYYx1hjqPUuKnWbVidNQp1AgmjojToavZKuT6iVVX3NQ2K22ENEGyTuamjXmU5d9yDy9Z0uTACUrvd/BDECjQBazq7+mGP0n0uHDHWHDHUY4Yx1GMdYYx1hjGOsMY6yxjrLO8Y6wxjHWGMdYYx1hjHWGOoWCqq4tU9j6RMTQX1l0SupRiIirtYa8bCV4h+9WE7iPpPBwgFfs+rOte9UD3ZQ9rtdfzLVHpoPBK8ruauXggTsyVv8AEr76fMPmLGKX4F0Xl9bqMdZcOGOox1hjrDhjHWGOsMY6wxjqOsMY6yxjHWGMdR1hjGOsMY6wxjrDGOsMdYZXQrVmpyk15KFY2dEHJ5UBtnC16QV89Ij2GG3yy9FXdR+Zd2priHxKj7XXx3glB9DXgiQ5uHl/E5xt2bfzLK/ujXllA2+yvkziF5f+fkw4fW6jhw5dRjrDHUYx1GMdYY6wxjrDGOo6wxjqOsMY6jrDGOo6wxjqOsMY6jqMY6wxjrDHWGOsrPV7WnulA+8bDyxtsOrD5d5zf6tpFGaeaePgj841svBOx/7n+I67F+TNFc3d/bOtY9cDyymTR0n8y+Q8I8EU0RE6JSR+g+p9DHWGOoxjqMY6wx1hjHWGMdR1hjHUdYYx1HWGMY6wxjHWGMdYYx1hjHWGOsMdYZxGOngW7Vl1p06jyy9Z8MfmUd9RfB8EQLnXW+CXVK1zyTu7l9vz2iMb2m3yy2Bqp0fLEq1NcY+JS/HkfHeVTXpp14JQqAIHT1j6HWXXodRw+lwx1GMdRjHWGOsMY6wxjHWGMdR1hjGOsMcOsMYx1hjHUdYY6wxjrDHWGOsXVMBK4++yvk9Y2i/Fk7EnV/AnRva/8JVsr0S38dJRu3a5fzNK3r68sbe3eA+e8W5Q74RKMTdZo+CUxvXU8Euj1bH+I4svIu4/QfW6jHUY6w4Yx1GMdYY6wxiYYxyxjqOsMYx1hjGOsMYx1hjHWGMdYYx1hjrDHWD1HdSjTGTCatwfzLxHpoPBAw7uauXgg99GeRfiFp1w5fl2nbg7uXyz7N4jyykQ2dJ/M6HzXwfBGf7RPgnvJfyT7k+P8Tb3AF7Nv0WOox1lwx1H0OoxjqMY6wx1hjHWGMdR1hjHUdYYx1HWGMY6wxjHWGMdR1hjrDGOsMdYY6wyu+LY+O8qD7Z14IxT2nM/iED8638y/fbQ15ZSvvsr5M2OL8E7KjVQ8Ev7ur6eCUjX9Et/Eql0r5fzLZr9bXlj14TYeD6Lhw+hjrDrDHUY4Yx1hjrDGOsMY6jrDGOo6jGMdR1hjGOsMYx1hjHWWOsMY6wx1hjrFo2QC7Q7KmwJ21brfxL6H6Wpore/vyx/uNXD5Z29LpP5ntag0eCURv8AM+CHjwdPMvxBzttKvPaXzv4v5eWW7utI8sOnFAlB7R9L63DhjqMdYY6jGOsMdYY4Yx1hjGOsMY6yxjqOsMYx1hjGOsMY6wxjrLHWGOsMdYYXa7Qm5Vv27H+I+pflNvlgDQuKfHyxvYdPGfiIHtoo+O8qTX6en2I8PtDl/EAMv1G38zq/k/lnm0SvkwXlUGh6I/Voj1Vy4dZdR9Dhwx16HDHDGOsMcMY6wxjrLGOo6wxjqOsMYx1hjGOsMY6wxjrDGOsMdYY6wwch1ohG1wVC73L7trhPnvOWp18IlNWHU8fBKK3odvgls+rrfxPaSXkxGqjfXfLH+81cHyylZo6T+ZdPTQ48BHUfWxw4cOHLrDhjhjHWGOsMY6wxjHWGMdR1hjHUdYYxjrDGMdYYx1hjHUdRjHWGOGOGOoyoMTyaX5ZV89F8B4Izw7XX8hLoz1zyQ4fuOZ/iNBvY2+WXamqnx8se2B1x/VOo+l1GOox1hjqMY6jGOsOWMdYYxjrDGOo6wxjqOsMYx1hjGOsMY6wxjrDGOsMdYY6wx1i00x5MgE1nCXbDTKqb3yvyxoB3mj5Z5xpQfPePodR9bHDh9THWGOoxjqMY6jGOsMY6wxjHWGMdR1hjGOsMYx1hjGOsMY6jqMY6wxjrDHWGOsMdRjrDhw/UdRjqMdRw4cusMdRjhjHWGOGMdYYx1HWGMdZYx1HWGMY6wxjHWGMdYYxjqMY6wx1hjrDhjr0OHLGPrYx1l9THUY5Y6jHLHWGMdYYx1ljHUdYYx1ljHUdZYx1hjHWGMdYYx1hjrDHUY4cuH0Pr/8QALBEBAAIBAwEHAwUBAQAAAAAAAQARITFBUWEQcYGRobHRMMHwIEBQYPHhgP/aAAgBAgEBPxD/AM2KC1hNFx3FS7VewDRm6Q9nlDbV/wAiE3reCOia1rDJZ/AngZYra+gisag4YP8AHBc6/mnpF6vZp6jpLCHZSelbGsvQWGrJi13CEib+fl++64+pV6D9QNGsu7y4FvCwf2+hbEXBeTyTD5wrNNtjmmGu04fVomqY17qN5UuyUOic8S9XuJ7MpcOuj37MG880/Zmh4Pq1KFTa68DWOb5Ze7Y8ZZDC8ghXH+9YLnXaNnDFnfT6awoNNtjnMTZevvQRqtXxr+3xhqy7+q7c5PpUqP1Vqdf2qm0EZzj2OE6MrbT7dPB9Gb/nIwxK8dP+HjqwBSl77P8AyVlKy6mz1IbVnY69HrK/Omz3jNfzx9xjr+crK5/N3zZnvbvovPcShN0fNfYiFjI40JduXO73aPhoz8+/ok3/ADlYCafmxPzrWx3urNHq52vg7uf2t0WxEf3DtS5fZcuOh+yrsq5iTbTZ8Nmfn4bXuP6AXrBNB8HTwZVYfJ+zOb8b+/2Ttp7KPz08OCU263vy97oEC8a9DTxZpho4DsrtLvGvTXw4JnMX0blSpUqVK+thefrX9S5cuXLl/VUCLpdta+DETVBcLeOMRjXGrsM67pPzvyn535T8b8p+N+Uwv3/KBCCUliWdHHkyo4kAlU2aMWYBaQz2AKvZe6aYt8SDj0bPJjZkLoNOKuJPq+oYqpUzljtYghsTn8Yy0U5l3RTtQV0FuUut+gQihQLV1brt2RaQSs6XZemZqzTsq/z7GPNZgN4DWz1lBqgbqt0rV4imtO57oroaFqmdPC3V6V1JroovgO+UIDS29KjdCbKefq3U4P3JsOy5cuXBQfTOzuMT2ftEEpitFu4robvSfgHzPwD5n4B8w/qAdDfxhFNmtvmIrYJa751pi5RVKmGniBxoXQrjPQqakoHZSK+RPszpPOOVXj/yG68AAtNDaVNIUZs3dTdY71A8ZdFs5TuvTQm4fIiFKWy9VfvEF8mnWpSlgLuzfWJLQeEf4L5lkRyqZfvDIsav5rKMlQb7wAA2iPEFZTx16suQIVpOmtdnRQTySerezLOMV5p8MrvNHr9V33n7mhZqfqqW6v1EQcw+INxfiOx+oRIjZVbf0DzSehD6C9u16E9px9GDXVgsDXV+CGvTbHcpBIlUM9y/adD5sXQwaeMBheF8uxweqiuXER/4PiNjNvY2U4gyIu9j4iLgr1ueGHuQoigO+5cRaLS78X4i0FAu+3YaIWBebIrynfQJdtsBFamzoXP8D4RoFI4DluZpqieZUIsW1we7eXynTYKdBT6vrH90ORhjqjs0Ih5OX6hFQ8M7sG93d9DiFpRE/T+K4J6f7RXZ6EjiYitL37ydN+O+Xs3lnvVjCMCN10SdN+O+MV0m1aX1Z6h7dn5rk7Kr9fcg3FCp1lz7teQQt4R6DLf2DyCFN+OT5iH8L2ezaOWbqvG8QGWEqGsuLy6VV57GYAvYJ1PkfE2TMNN7vTugvuH6prvv43PRP6fxXBPT/bsE9CSoakVsvfvJ0PI/Mrutd1phSVRWoZ4pftOl5H5ihTIqitb6s9Q9uz8RySwLZ7v3SrDtprnyJgjbdl+0s7nQp+52PgLA8MfYnfQDze11TKHtMMVfwnoX2exXXsMvqC6uumk9c+rQXk+lf8Hnon9P4bgnp/t2vQktAsVhqdfzHxN/Iq+9YUsobxjZPvOr5j4hItou29J6h7dhvuvsnSsfae7909N2BNNFV4tWHcZYO/nwjGtXfhoSomq2+GD3eykME1nWyHRoMBKjSXWu+s3BC+uwdlrnq7M63yZilOormeufVyRqZ/izsz0T+n8VwT0/27QVFB1Z/oM/0GLWq3a94jxFQsaxTP8AQZ/oMcNRwivYb7h7zu4TzxPd+6emi1BfVv0AJVEI2MsJ3DW4Dg6sW64B5BFU0cHQNOxJox23R7TAGv8AEuFvD7PYg0+j4T858JxqLwmt1qHE9c+tZm238UdmZvuY0GDVRDzqArROsnUQzqQE4Qnp/t2NjVZd6az88T88T88TCwrpOJrrX3n44n54n54lMOlvvGobW+QykNX7Z96nu/dPTfZNvX72Ij7pmIoZV91l9sNRNfB1IlOtxtXJx2+kTIml3QtacS9GxSxLx17PXMv0WtEy3tVX0hV3j6xU7mkRVOv1b4Mv7s7EVdAkKts8PhjcNijEYDwzoeQ+Z0vIfMCYR7j5hIrYGvj+gex9Wl3RyrGabA5Oiff9OSNAmIjZQwHB8sBMC7Kd1eIiYg3rvUTMJ4HzF08dfsRvKnGgeEy/wRrw+JU7Amhud/aZuRomodRnXfKJskNscgbpxP8ALPmKx6ytKlJir25ScHlEJLxrm1+jtdrBpbK5WqyGgwTGVV1vX6DP+iIqn6Ws6n2OP39y+y+y/wBV/Qv6Vy5f6b+hiwBrxjF1UDEIPMHhNKveoqMKrF6X1iRx0zWlzKJpdb12DZw8zXjHP0G6EzDl+goLYuBSXYf5m/1vvwOCW1hesSuZu295TJtRyaRUHGKPeUa3TCDQdFlfuKq964vt1mbMPSA1DKt067yYjdE60TkUACj6LoO0o/wV/sr+phQoUMItbyvrMfRTIadJjRWxuxtuXSKHU3ct9Fq72riX3NNC68Ym1h/Z6BtLlxv6BBgBw1zLm91o9IgJC3rEwQ6LqQjbmb56zHjlWnSYwF6HPxBwGmIjn9pnNIHdhGj+gXZDGKWvMtIsBWx1hI2hoasRNZhlnM5Rxie3dDSsOfmLnJLtr0jFC5lMukSLQCJx40CKVJX9QDHczMGjo46xAwG17sFFKGMRVWtsLeRS7RQZUYOYxznGtPE3H4EBowTG5PpG7VzLOCaezzAFZvrGrx/UXtSddnpEDKXbAxDbbjhjBgGIayquIRhUx2T6TV3hMroczK6vM1N4bzF4HrHqFsEzl0i9B0/qYwctuswOr0mstHE08xzMw5YRaqYzE53gwLWbnlgGjBMBk+kbtXNP6k1xgI/RmTyfSAWtE2z4y+QsdnDpvCKFTHGX0mrscTppyzK6vMyvOf6ijA1ZnMniXsghGBfWKWtsyeB6wehMbqzWcHE08xzM85YVaqYzA53lrl/qLq7mY3J9I/amWcE09nmaq54mOMEXwmTyfSIbWibXnl5GWOzgcbygGs/1LO6HLOuvLNTTF4HrHqFsMzl0nRBDMZdY5atmbwPWH4TC6vE1bTj+pUybTGYHrEMZZuPwgNGCYvJ9I/amWcE09nmau54mOMEXoTUF1/UgVojM49N4RQqY4yzV2OJldDmZXV5g+UxeB6xyhbAM5dIxdAiqt/qQo2TKsY1aOJp7HMyzlhFqo7GJzvAwMs3PLCKCiYvJ9I/a/qrnoOkAtaJtnxi5FrHZw6bwihUxxlmruOP6w6huNZURnkfebhb0m+0cf+AT+D//xAArEQEAAgECAgoDAQEBAAAAAAABABEhMVEQQSAwYXGBkaGxwfBAUNHh8WD/2gAIAQMBAT8Q/MOifhEOgQhDgdccT9idUdQdUQ/8CQh1hwOgcDoHROB+AfknXnVEIdA4HTPyToIbVELgXOe13R1Svjw0BSby78wnBrtIHav9YdG6lqUW9hfaBhSzph/kBvxOBDpn4ZMYz7I/buXLly5cuXGLVM5Yu/J/THUuhgTTud9lEZGi1TzsZcQ8kec9G2adQBW86wSmqleYHLDKA0Xp2whwOmcT8Dt/zdv9ly5cuXLly5cuXMB8cn4ekdJc8CaacNVI+eR1Z1gWhbV1MnbGmzo29upUpljy5u9OiTFgRp5vNo1uKarM64aOd6PpFyvU5c/LWDVWXRHA7d8W7TXIbbMpcOrh79R8YN53t60zQ8H1aiCo6XXgMzm0Xl7uR4zInIzsE1F8qF3296Z5iy6Wcjdl4tUs7CnzbHtg4YeXN3p5ytWk17edGufWNm8mcnZqpyIfjYM1af2XfW01MnPc6oFbodIAqzTra4BKlSpUqIqNDVq6N4A6HPfPK3MdGVy0+OzwZeb+6j/YWffL1YHIyY8a0PFtZilqGimG9ZWUql1OT2nbDlWeRfM5rtlWa4rXsvD3jhmvr659xnPx+V+JWM/eb5tEze7d9i817IoSso+a37CJekFtyS71c4tOzR8NGV4fbrwSaP3dYCafawetsOy6wdtcjv1MoseNaX/HOVwqV+CoFdCMi8O6XLly5cuXLly5cuXL6pUHSAqV1RCEDgEqVwqXRcqiK1PJHYdENKh9/wCcr5rDwq+ALoX9+faiFGBrsdPBiDIo2fhm9+N/J7CH3/e8185UpNeFFfa7O2tiUqmbdd3vdBA7Q2NPF5zJho2DhXLh9+vK+b4ELHF32a12chKKCq1K0Oy+yEOJDrsJ19nOXLly5cuXLly5cuXLl/h3zpkIQRUNCgcla2kpEqAUKy1nMK34sKWFS7BNTo5ZZZOGjec+IB8MRyCOjQElQRL0VUpMnz5RPyhYqixqYF0T5mXJe7WXRVu5Lg/4RPJjFQoVbsgROsCt3a+0qljk3e+aUo98W+i9SbJPON2Gi1a7w260VYN3yIUpkU8A8olYChdWhdXTV8AMWLY1pFKuWiqTUlQLx8X6GPNZkkqS8Ch2I5zzS+QG7o3gsskUMNWDrtrEPYBdmLZRa8FXzC3dM0q1F5VaF1IRZUKEHItxzg0FRqhet7hHvn8laekp211RCBB3pvufMFQmpGTAKb35A7z6rH1f4n3X+RA2pNXk1Cg9C9XcNoGlEUcKFYEn3v8AyN7++wMizSyN3NpSyAcUsAPxFTPojGL3BFGnpIWNLdrfMubRVcytuijsqrVLcjS7oqNUwMF1fjBzksFALBygw2Haw8qrg0KD4Qj94tqVV7RodIlaV8nSo2zV5Fp90+J2X2APIIZgHhALhdGq7d0NAFW10C9ojPVbmSk2g0NA4OYDFhhAqC6dOBG9UvNJ9zsg8/v8EH1hU8inzB6pK6zxn7Y4ly5fBfBfBfQF9ULLOkBgt16ohFGWgXFrNViFY8YaIbURe7NMBigANgwHEi3Wh+SiM8mwqBPsN2Fea7XQwS8VbyAB4qx7w1nXI+UQaVsA6IfM/wCWRa1atgVVR4VKTwcJKh8DVbWFGVazirgqh88CkA8wDvE4LDnzX2wM8Xo7qCEG8l9GJSVhyaikNz0Oy7ob9Yz0UBk5tSo3aoobB8kgtjLyGjk1YEMAaEQs1oRUEQOTgwaa9CpoEqi/eIOXVTekaiKRvWRV1fKY9ghahNUFezY61eY+/C+Fy+Fy5fC5cvrdYax1xw04h5uXqzgt9wnnP5DGHa3dxjaQTweJDg49X4JPsN2KwzLGuQTtPk/kqE9K7qh8IpgrQa1Ru6dp23yfyLxrTLd3U++36EhhtmISBFguZyEbqi8yu3nven4CKLR/NQ9rleuE+aZZ1/4jBv5+y4BjbkZZWaVmLKUp2wxW8Naju/1ndaK2cFUIDaTU2QTsfn/UwX19K3WOr2xKj7PWir23556F8b6Vy5f5R0AfUnvxIcHHq3BJ9huxYgsCkJ/1T+S2rTW25C20bxF2yKQbA9lnb+V/I+iCm0dEqqCffb8VCENXHDQVHFdIUumqTJPFdGQyUsdwa15fhZ4WJqFmyl+7jhrCV4FPWuBKzIEec1saE+u4QQFB2T/mf5ObxVsurrHYT6jc63bwep/lS5cvguX0BfBcuXL/ACjgJph9Se/AhxOPVuCT7DdgzIYti+yTsfOiuTHdFGACi3kEJgUKxSlHknMnZ+ZB5BKFCa1raz77fgxT/wBlOzVeaHDT1nz4ZjGxRoVp8mMkyC8qOnfogNbEPeW+Ra8IorAD3tL5UefB8jgUDRSsm5L5SVd1mSuO6BurrUd2Frb6WBV2aHOuB7dE5Nu+f8d/ZhrTKkdZ+o3OtvRrn4c/1JwHAfUnvxIcHHq3BIpIq2tMsP8AHQ/z38hdcYBQWzDaEkEEumf81/J/zUSBFiARNEZcQW7/AGjH8i/Tu4aes+cakh1qj5qpC6lc3B4XnwRN5BF1o1TlA2S/vVX3WDvILW618PiXE1ATtzAEYPmmDT2iKbjOkrgFDB4TTf31lXVXqa1J9RudbQlMReZk7pcuXLly5cuXLly5cLfyiHBphQM6We8JtmACr3A2xQK6EP8AqQ/1GLLaYnMUjPU43Fq+irW65Vm+hkszLflenO86VM/lN114dBJN3WS+4lxagDxEVoyM9uHtwOep+cHEwc9KKL+xsHevxM78QAcGwJR1WhyDWljhnJDaajs7jwJ9hvNuEUWrOrZkhl6KWKul4Pyz2ldl1W6W6HpMKtdXK8YUR9nri2DI/HcxgVJqS5cuXLly5cuXLhnSN/iRt1B+EQihpW9qr8E2ZRq25/RL9AtoLUQjVE9J23nR2/nRUDt7X2h4MLbVFHI4XBgwYtBiW04BAeBMKbyp7EvhcuN6LBy0UMLJa2t3s7AiOsY2ZonVg2rdLRujYxbCO/4QNF3cq9UVo01XK71mD1QltGLHNPJlE25hXhHaXBgltWWpQ1EJW/Cl9ssVVkm6Gf8AbmiGoB5EPCBbSFAOQsOdT3xFZKoCrClza9TYekC4xpn17GMApNRly5cuXLly5cLWiY4dg27X8shCDDhcuDxvgMGDBly5cuDBhB4Ll8Lly4MGDL43Lly+ndCoRO8EdOJdXKj3uHLTkTXx3IhqbjTqKBZu8jvZSd88juOoYghuC3eKFJC2P4RCDBly5cGXLl8CDBgy5cvgQYMGXBgy5cuXB4XBl8L6lvDmaClYol7Y1AlFCFDL9zjQlOkyA7rTymjw8ma8/Cn2uf8AQfyaS/HHvNQB77ZlrvtweRDAAHIwdSrLx2xXQOtIQg8L6dy5fG+Fy+idK5cIdaGVGHzRkxbsdQXKxUS0yihhXX8O0McZV1R1Q9G5cvqyHROkdE6mmUazOQC1eZZkFvNCR5JiSUJD8QctIbmAFH6UhwIQ6g64WkgAomUMRAVzi2sFocyzCBU1S7gmyiYM0busrirA5x7VnzlhlhVoT9OQ4HA6B1ZDonX3tzRJeTtilMoO5ADE1ViGoKwDvnIPi/BLYlPGZzBtzlEFTA+A08Waio2NIzQt3Ty5wus6/pD8M6Z+G5bEcIQeSG6qEKFu7pLErfuhMt8zyg+Od+cwi3sJhFrYfLEqxb8pV/CeUsSAmCaN3Xyh5NGp8+P6E6Z1BDgfoAAOrr3TIV2j8EyAW7sJyzsazC+A185Sjft4so0ttyjZqB4Tkvi/BLYlJnMG3OVQV7+cG/0JDpHSOidQfj24Wb8u6DW67OcweL1lECmV5b7D5YMOg8JgG3flLE7fuhM58jyhuGd+cwq3sJhL7A+WOA81eH6o6g4nVnE65xoEwoo3dYGLaeLKBaNjXzlaAE5ivSLW/Dl5TIV2j8EzIXuYTlnY1mB8Br5ylXfebM+225QBQUfpzonUnVnXnAqZnEqMLbnKYK+7zB+A085rrGxpNNUbukzr3j8EGtV2c/KYPF6/5KoGHPfA+WCmUHhLIbd+UdJeHu8v0Z+CQ/MJhFvYTAXWw+Y9jjflMnk9PKWpgTANG7r4Q36eLKBaNjXzlaAEs/hPOLW8bcpmK7R+CZbLc9efozon5b2jn0e2Z9W25RsGgTkPi/BL4lTOYtucqgr3mB7gf2aio2NJoqjd0/2ZnvD8Euir38pUUodO79IcDqiHQOJ0zrTpWBXBMAW78pYkr90JkvEa+UFwzvzmEW9hMJfYHyx6jjflOdr08pemBMA0buvgQMlV1XaAQ0P0R0DqziQ4HUHA/BQFOjLEVbviZAL3MJyzsazA/I85Ujb91Zmm23KNm0HhOQ+L8EuiYZ/Ftzh1Gvf9CdcfgEOJxOvExrcayuBTOe+B8sFIoJgi2/KXJ2/dCZnxGvlCcc7uv4p+KfgHWH4WlA7pdrnfUCWlbBicjpu/yZwL3P6o6o/DOJ1Z+/OJ1h0j9sdA686k6Z+WdP8A/9k=');
	width: 100%;
	height: 300rpx;
	background-repeat: no-repeat;
	background-size: 100% 100%;
	background-color: var(--view-theme);
}

.OrderCancellation {
	width: 100%;
	height: 100%;
	background: #fff;
}

.OrderCancellation .whiteBg {
	width: 90%;
	background-color: #fff;
	margin: -93rpx auto;
	padding-top: 80rpx;
	border-radius: 6rpx 0;
}

.OrderCancellation .whiteBg .input {
	width: 90%;
	margin: 0 auto;
	border-bottom: 1rpx solid #eee;
}

.OrderCancellation .whiteBg .input input {
	font-size: 60rpx;
	color: #282828;
	width: 100%;
	text-align: center;
	line-height: 80rpx;
	height: 80rpx;
}

.OrderCancellation .whiteBg .bnt {
	font-size: 32rpx;
	color: #fff;
	width: 80%;
	height: 86rpx;
	border-radius: 43rpx;
	background-image: linear-gradient(to right, #f67a38 0%, #f11b09 100%);
	text-align: center;
	line-height: 86rpx;
	margin: 55rpx auto 0 auto;
	// background: var(--view-theme);
}

.OrderCancellation .scan {
	width: 300rpx;
	height: 300rpx;
	margin: 160rpx auto 0 auto;
}

.OrderCancellation .scan image {
	width: 100%;
	height: 100%;
	display: block;
}

.WriteOff {
	width: 560rpx;
	height: 860rpx;
	background-color: #fff;
	border-radius: 20rpx;
	position: fixed;
	top: 50%;
	left: 50%;
	margin-top: -400rpx;
	margin-left: -280rpx;
	z-index: 99;
	padding-top: 55rpx;
}

.WriteOff .pictrue {
	width: 340rpx;
	height: 340rpx;
	margin: 0 auto;
}

.WriteOff .pictrue image {
	width: 100%;
	height: 100%;
	display: block;
	border-radius: 10rpx;
}

.WriteOff .num {
	font-size: 30rpx;
	color: #666;
	margin: 28rpx 0 30rpx 0;
}

.WriteOff .num .see {
	font-size: 16rpx;
	color: #fff;
	border-radius: 4rpx;
	background-color: #c68937;
	padding-left: 5rpx;
	margin-left: 12rpx;
}

.WriteOff .num .see .iconfont {
	font-size: 15rpx;
}

.WriteOff .tip {
	font-size: 36rpx;
	color: #282828;
	text-align: center;
	border-top: 1px dashed #ccc;
	padding-top: 40rpx;
	position: relative;
}

.WriteOff .tip:after {
	content: '';
	position: absolute;
	width: 25rpx;
	height: 25rpx;
	border-radius: 50%;
	background-color: #7f7f7f;
	right: -12.5rpx;
	top: -12.5rpx;
}

.WriteOff .tip:before {
	content: '';
	position: absolute;
	width: 25rpx;
	height: 25rpx;
	border-radius: 50%;
	background-color: #7f7f7f;
	left: -12.5rpx;
	top: -12.5rpx;
}

.WriteOff .btn {
	font-size: 32rpx;
	text-align: center;
	line-height: 82rpx;
	height: 82rpx;
	width: 460rpx;
	border-radius: 41rpx;
	margin: 40rpx auto 0rpx auto;
	color: #fff;
}
.sure {
	background-image: linear-gradient(to right, #f11b09 0%, #f67a38 100%);
	background-image: -webkit-linear-gradient(to right, #f11b09 0%, #f67a38 100%);
	background-image: -moz-linear-gradient(to right, #f11b09 0%, #f67a38 100%);
	background-color: var(--view-theme);
}
.WriteOff .cancel {
	// background-image: none;
	// color: #999;
	margin-top: 10rpx;
	border: 1px solid #f11b09;
	color: #f11b09;
	margin-top: 20rpx;
}

.views {
	font-size: 18rpx;
	background: #c68937;
	border-radius: 4px;
	color: #fff;
	padding: 5rpx 2rpx 5rpx 8rpx;
	margin-left: 10rpx;
}

.views-jian {
	font-size: 10px;
}
</style>
