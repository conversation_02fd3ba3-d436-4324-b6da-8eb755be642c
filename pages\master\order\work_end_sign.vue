<template>
  <view class="work-end-sign">
    <!-- 顶部导航 -->
    <view class="page-header">
      <text class="title">离场签到</text>
      <text class="subtitle">请填写离场信息完成签到</text>
    </view>

    <!-- 加载状态 -->
    <view class="loading-container" v-if="loading">
      <view class="loading-content">
        <text class="iconfont icon-loading loading-icon"></text>
        <text class="loading-text">加载中...</text>
      </view>
    </view>

    <!-- 表单内容 -->
    <view class="form-content" v-else>
      <!-- 人数输入 -->
      <view class="form-card">
        <view class="form-item">
          <view class="label-box">
            <text class="required">*</text>
            <text class="label">{{ formData.end_num.name }}</text>
          </view>
          <view class="number-input-box">
            <view class="number-control minus" :class="{ disabled: formData.end_num.value <= 1 }" @tap="handleNumberChange(-1)">
              <text class="iconfont icon-jianhao1"></text>
            </view>
            <input type="number" v-model="formData.end_num.value" class="number-input" @blur="validateNumber" placeholder="请输入人数" />
            <view class="number-control plus" @tap="handleNumberChange(1)">
              <text class="iconfont icon-jiahao1"></text>
            </view>
          </view>
          <text class="tip-text">请输入实际离场施工人数（至少1人）</text>
        </view>
      </view>

      <!-- 施工进度 -->
      <view class="form-card">
        <view class="form-item">
          <view class="label-box">
            <text class="required">*</text>
            <text class="label">{{ formData.progress_list.name }}</text>
          </view>
          <view class="progress-list">
            <view class="progress-item" v-for="(item, index) in formData.progress_list.child" :key="index">
              <text class="product-name">{{ item.name }}</text>
              <view class="progress-options">
                <!-- 已完成选项 -->
                <view class="status-section">
                  <text class="section-label">完成状态</text>
                  <view class="options-group">
                    <view
                      class="option-item done-option"
                      :class="{
                        active: formData.progress_list.child[index].value.includes('0'),
                        disabled:
                          formData.progress_list.child[index].value.length > 0 && !formData.progress_list.child[index].value.includes('0')
                      }"
                      @click="toggleDone(index)"
                    >
                      <text class="option-text">已完成</text>
                      <text class="iconfont icon-check" v-if="formData.progress_list.child[index].value.includes('0')"></text>
                    </view>
                  </view>
                </view>

                <!-- 进行中状态 -->
                <view class="status-section">
                  <text class="section-label">进行状态</text>
                  <view class="options-group">
                    <view
                      v-for="option in progressOptions"
                      :key="option.value"
                      class="option-item"
                      :class="{
                        active: formData.progress_list.child[index].value.includes(option.value),
                        disabled: formData.progress_list.child[index].value.includes('0')
                      }"
                      @click="toggleProgress(index, option.value)"
                    >
                      <text class="option-text">{{ option.label }}</text>
                      <text class="iconfont icon-check" v-if="formData.progress_list.child[index].value.includes(option.value)"></text>
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 离场确认 -->
      <view class="form-card">
        <view class="form-item">
          <view class="label-box">
            <text class="required">*</text>
            <text class="label">{{ formData.confirm_list.name }}</text>
          </view>
          <view class="confirm-list">
            <view class="confirm-item" v-for="(item, index) in formData.confirm_list.child" :key="index">
              <!-- 单选按钮类型 -->
              <template v-if="item.type === 'radio'">
                <view class="radio-item">
                  <text class="radio-label">{{ item.name }}</text>
                  <view class="radio-options">
                    <view
                      class="radio-option"
                      :class="{ active: formData.confirm_list.child[index].value === 1 }"
                      @click="setConfirmValue(index, 1)"
                    >
                      <text class="option-text">是</text>
                      <text class="iconfont icon-check" v-if="formData.confirm_list.child[index].value === 1"></text>
                    </view>
                    <view
                      class="radio-option"
                      :class="{ active: formData.confirm_list.child[index].value === 0 }"
                      @click="setConfirmValue(index, 0)"
                    >
                      <text class="option-text">否</text>
                      <text class="iconfont icon-close" v-if="formData.confirm_list.child[index].value === 0"></text>
                    </view>
                  </view>
                </view>
              </template>

              <!-- 视频上传类型 -->
              <template v-else-if="item.type === 'video'">
                <view class="video-item">
                  <text class="video-label">{{ item.name }}</text>
                  <view class="video-upload" @tap="chooseVideo(index)" v-if="!item.value">
                    <text class="iconfont icon-xiangji"></text>
                    <text class="upload-text">点击录制视频</text>
                  </view>
                  <view class="video-preview" v-else>
                    <video :src="item.value"></video>
                    <view class="video-actions">
                      <button hover-class="button-hover" class="action-btn" @click="chooseVideo(index)">重新录制</button>
                      <button hover-class="button-hover" class="action-btn delete" @click="removeVideo(index)">删除</button>
                    </view>
                  </view>
                </view>
              </template>
            </view>
          </view>
        </view>
      </view>
      <!-- 确认声明 -->
      <view class="form-card">
        <view class="form-item">
          <view class="agreement-box" @click="toggleAgreement">
            <text class="checkbox" :class="{ checked: formData.note.value === 1 }">
              <text class="iconfont icon-xuanzhong3" v-if="formData.note.value === 1"></text>
            </text>
            <text class="agreement-text">{{ formData.note.name }}</text>
          </view>
        </view>
      </view>
      <!-- 提示信息 -->
      <view class="notice-section">
        <text class="notice-icon iconfont icon-tishi"></text>
        <text class="notice-text">请确保信息真实有效，签到后将无法修改</text>
      </view>
    </view>

    <!-- 底部按钮 -->
    <view class="footer">
      <button
        class="submit-btn"
        :class="{ 'btn-disabled': !canSubmit || submitting }"
        @click="submitSign"
        :disabled="!canSubmit || submitting"
      >
        <text class="iconfont icon-submit" v-if="!submitting"></text>
        <text class="iconfont icon-loading loading-icon" v-else></text>
        <text>{{ submitting ? '提交中...' : '确认签到' }}</text>
      </button>
    </view>
  </view>
</template>

<script>
import { getOrderSign, orderSign } from '@/api/worker'

export default {
  data() {
    return {
      orderId: '',
      formData: null,
      loading: true,
      submitting: false,
      progressOptions: [
        { label: '拆包', value: '1' },
        { label: '组装', value: '2' },
        { label: '调试', value: '3' }
      ],
      showActions: false
    }
  },

  computed: {
    canSubmit() {
      if (!this.formData) return false
      // 检查人数
      if (!this.formData.end_num.value || this.formData.end_num.value < 1) return false
      // 检查施工进度
      const hasProgress = this.formData.progress_list.child.every((item) => item.value.length > 0)
      if (!hasProgress) return false
      // 检查离场确认
      // const hasConfirmed = this.formData.confirm_list.child.every((item) => {
      //   if (item.type === 'radio') return item.value === 1
      //   if (item.type === 'video') return !!item.value
      //   return true
      // })
      // if (!hasConfirmed) return false
      // 检查确认声明
      if (this.formData.note.value !== 1) return false
      return true
    }
  },

  onLoad(options) {
    this.orderId = options.orderId
    this.loadFormData()
  },

  methods: {
    async loadFormData() {
      try {
        const res = await getOrderSign()
        if (res.status === 200) {
          // 初始化表单数据
          this.formData = res.data
          // 设置默认值
          this.formData.end_num.value = 1
          this.formData.progress_list.child.forEach((item) => {
            item.value = []
          })
          this.formData.confirm_list.child.forEach((item) => {
            item.value = item.type === 'radio' ? 0 : ''
          })
          this.formData.note.value = 0
        } else {
          this.$util.Tips({ title: res.msg || '获取表单数据失败' })
        }
      } catch (error) {
        this.$util.Tips({ title: error.msg || '获取表单数据失败' })
      } finally {
        this.loading = false
      }
    },

    handleNumberChange(delta) {
      if (!this.formData) return
      const newValue = parseInt(this.formData.end_num.value || 0) + delta
      if (newValue >= 1) {
        this.formData.end_num.value = newValue
      }
    },

    validateNumber() {
      if (!this.formData) return
      let value = parseInt(this.formData.end_num.value)
      if (isNaN(value) || value < 1) {
        this.formData.end_num.value = 1
      }
    },

    toggleDone(index) {
      const item = this.formData.progress_list.child[index]
      // 如果已经选择了其他进度，则不允许选择已完成
      if (item.value.length > 0 && !item.value.includes('0')) return

      if (item.value.includes('0')) {
        item.value = []
      } else {
        item.value = ['0']
      }
    },

    toggleProgress(index, value) {
      const item = this.formData.progress_list.child[index]
      // 如果已经选择了已完成，则不允许选择其他进度
      if (item.value.includes('0')) return

      const valueIndex = item.value.indexOf(value)
      if (valueIndex > -1) {
        item.value.splice(valueIndex, 1)
      } else {
        item.value.push(value)
      }
    },

    setConfirmValue(index, value) {
      this.formData.confirm_list.child[index].value = value
    },

    // 选择视频
    async chooseVideo(index) {
      this.$util.imageUploadConfirm().then((result) => {
        if (result) {
          this.$util.uploadVideoOne(
            {
              url: 'upload/image',
              count: 1,
              sourceType: ['camera']
            },
            (res) => {
              console.log('🚀 ~ chooseVideo ~ res:', res)
              this.formData.confirm_list.child[index].value = res.data.url
            },
            (error) => {
              console.log('取消选择视频')
            }
          )
        }
      })
    },

    removeVideo(index) {
      this.formData.confirm_list.child[index].value = ''
    },

    toggleAgreement() {
      this.formData.note.value = this.formData.note.value === 1 ? 0 : 1
    },
    async submitSign() {
      if (!this.canSubmit || this.submitting) return

      this.submitting = true
      try {
        const res = await orderSign({
          order_id: this.orderId,
          type: 'work_end',
          end_content: JSON.stringify(this.formData)
        })
        console.log('🚀 ~ submitSign ~ res:', res)
        if (res.status === 200) {
          this.$util.Tips({ title: '签到成功' }, { tab: 3 })
          // 返回上一页并刷新列表
          const pages = getCurrentPages()
          const prevPage = pages[pages.length - 2]
          if (prevPage && prevPage.$vm.loadCheckInList) {
            prevPage.$vm.loadCheckInList()
          }
        } else {
          this.$util.Tips({ title: res.msg || '签到失败' })
        }
      } catch (error) {
        this.$util.Tips({ title: error || '签到失败' })
      } finally {
        this.submitting = false
      }
    },

    showVideoActions() {
      this.showActions = true
    },

    hideVideoActions() {
      this.showActions = false
    }
  }
}
</script>

<style lang="scss">
.work-end-sign {
  min-height: 100vh;
  background-color: #f7f8fa;
  padding-bottom: 120rpx;

  .page-header {
    background: #f4511e; // 深橙色
    color: #fff;
    padding: 60rpx 40rpx;
    position: relative;

    &::after {
      content: '';
      position: absolute;
      left: 0;
      right: 0;
      bottom: -20rpx;
      height: 20rpx;
      background: linear-gradient(to bottom, rgba(0, 0, 0, 0.05), transparent);
    }

    .title {
      font-size: 44rpx;
      font-weight: 600;
      margin-bottom: 16rpx;
      display: block;
    }

    .subtitle {
      font-size: 28rpx;
      opacity: 0.9;
    }
  }

  .loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 400rpx;

    .loading-content {
      text-align: center;

      .loading-icon {
        font-size: 48rpx;
        color: #f4511e;
        display: block;
        animation: rotate 1s linear infinite;
      }

      .loading-text {
        font-size: 28rpx;
        color: #666;
        margin-top: 20rpx;
      }
    }
  }

  .form-content {
    padding: 28rpx;
  }

  .form-card {
    background: #fff;
    border-radius: 24rpx;
    padding: 28rpx;
    margin-bottom: 30rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
    border: 1rpx solid rgba(244, 81, 30, 0.1);

    &:last-child {
      margin-bottom: 0;
    }
  }

  .form-item {
    .label-box {
      display: flex;
      align-items: center;
      margin-bottom: 30rpx;

      .required {
        color: #f4511e;
        margin-right: 12rpx;
        font-size: 32rpx;
      }

      .label {
        font-size: 32rpx;
        color: #333;
        font-weight: 600;
      }
    }

    .number-input-box {
      display: flex;
      align-items: center;
      justify-content: space-between;
      background: #f7f8fa;
      border-radius: 12rpx;
      padding: 20rpx;
      margin-bottom: 16rpx;

      .number-control {
        width: 80rpx;
        height: 80rpx;
        border-radius: 50%;
        background: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
        transition: all 0.3s;

        &.disabled {
          opacity: 0.5;
          background: #f5f5f5;
        }

        &:active {
          transform: scale(0.95);
        }

        .iconfont {
          font-size: 40rpx;
          color: #f4511e;
        }
      }

      .number-input {
        flex: 1;
        text-align: center;
        font-size: 40rpx;
        font-weight: 600;
        color: #333;
        margin: 0 30rpx;
      }
    }

    .tip-text {
      font-size: 26rpx;
      color: #999;
      margin-top: 16rpx;
      display: block;
      padding-left: 10rpx;
    }
  }

  .progress-list {
    .progress-item {
      margin-bottom: 40rpx;
      background: #f7f8fa;
      border-radius: 16rpx;
      padding: 30rpx;

      &:last-child {
        margin-bottom: 0;
      }

      .product-name {
        font-size: 30rpx;
        color: #333;
        margin-bottom: 24rpx;
        display: block;
        font-weight: 500;
      }

      .progress-options {
        .status-section {
          margin-bottom: 24rpx;

          &:last-child {
            margin-bottom: 0;
          }

          .section-label {
            font-size: 26rpx;
            color: #666;
            margin-bottom: 16rpx;
            display: block;
            padding-left: 8rpx;
          }
        }
      }

      .options-group {
        display: flex;
        flex-wrap: wrap;
        gap: 20rpx;

        .option-item {
          padding: 16rpx 30rpx;
          border-radius: 40rpx;
          font-size: 28rpx;
          color: #666;
          background: #fff;
          border: 2rpx solid #e5e7eb;
          display: flex;
          align-items: center;
          transition: all 0.3s;

          &.done-option {
            background: #fff;
            border-color: #e5e7eb;

            &.active {
              background: #f4511e;
              border-color: #f4511e;
              color: #fff;

              .iconfont {
                color: #fff;
              }
            }

            &.disabled {
              background: #f5f5f5;
              border-color: #e5e7eb;
              color: #999;
            }
          }

          &.disabled {
            opacity: 0.5;
            background: #f5f5f5;
            cursor: not-allowed;

            &:active {
              transform: none;
            }
          }

          .option-text {
            margin-right: 10rpx;
          }

          .iconfont {
            font-size: 28rpx;
            color: #f4511e;
          }

          &.active {
            color: #f4511e;
            background: rgba(244, 81, 30, 0.08);
            border-color: #f4511e;

            &.done-option {
              background: #f4511e;
              color: #fff;
            }
          }

          &:active:not(.disabled) {
            transform: scale(0.98);
          }
        }
      }
    }
  }

  .confirm-list {
    .confirm-item {
      margin-bottom: 30rpx;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .radio-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 24rpx;
      background: #f7f8fa;
      border-radius: 12rpx;

      .radio-label {
        font-size: 28rpx;
        color: #333;
        flex: 1;
      }

      .radio-options {
        display: flex;
        gap: 20rpx;

        .radio-option {
          min-width: 120rpx;
          height: 64rpx;
          border-radius: 32rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          background: #fff;
          border: 2rpx solid #e5e7eb;
          transition: all 0.3s;

          .option-text {
            font-size: 28rpx;
            color: #666;
            margin-right: 8rpx;
          }

          .iconfont {
            font-size: 24rpx;
          }

          &.active {
            border-color: #f4511e;

            &:first-child {
              background: #f4511e;
              .option-text,
              .iconfont {
                color: #fff;
              }
            }

            &:last-child {
              background: #fff5f5;
              border-color: #ffcdd2;
              .option-text,
              .iconfont {
                color: #f44336;
              }
            }
          }

          &:active {
            transform: scale(0.98);
          }
        }
      }
    }

    .video-item {
      .video-label {
        font-size: 28rpx;
        color: #333;
        margin-bottom: 16rpx;
        display: block;
      }

      .video-upload {
        width: 100%;
        height: 180rpx;
        background: #f7f8fa;
        border-radius: 12rpx;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        border: 2rpx dashed #dcdfe6;

        .iconfont {
          font-size: 48rpx;
          color: #999;
          margin-bottom: 12rpx;
        }

        .upload-text {
          font-size: 26rpx;
          color: #666;
        }

        &:active {
          background: #f2f3f5;
        }
      }

      .video-preview {
        position: relative;
        width: 100%;
        border-radius: 12rpx;
        overflow: hidden;

        video {
          width: 100%;
          height: 360rpx;
          background: #000;
          vertical-align: bottom;
        }

        .video-actions {
          display: flex;
          padding: 24rpx;
          background: rgba(0, 0, 0, 0.75);
          backdrop-filter: blur(10px);
          gap: 24rpx;
          .action-btn {
            flex: 1;
            height: 76rpx;
            border-radius: 38rpx;
            font-size: 28rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
            font-weight: 500;

            &:first-child {
              background: rgba(255, 255, 255, 0.2);
              color: #fff;
              border: 1px solid rgba(255, 255, 255, 0.3);
            }

            &.delete {
              background: rgba(244, 81, 30, 0.9);
              color: #fff;
            }
          }
        }
      }
    }
  }

  .agreement-box {
    display: flex;
    align-items: center;
    padding: 20rpx 0;

    .checkbox {
      width: 40rpx;
      height: 40rpx;
      border: 2rpx solid #dcdfe6;
      border-radius: 8rpx;
      margin-right: 20rpx;
      display: flex;
      align-items: center;
      justify-content: center;

      &.checked {
        background: #f4511e;
        border-color: #f4511e;

        .iconfont {
          color: #fff;
          font-size: 28rpx;
        }
      }
    }

    .agreement-text {
      font-size: 28rpx;
      color: #333;
    }
  }

  .notice-section {
    display: flex;
    align-items: center;
    padding: 20rpx;
    background: rgba(244, 81, 30, 0.1);
    border-radius: 8rpx;
    margin-top: 30rpx;

    .notice-icon {
      font-size: 32rpx;
      color: #f4511e;
      margin-right: 12rpx;
    }

    .notice-text {
      font-size: 24rpx;
      color: #666;
      flex: 1;
    }
  }

  .footer {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    padding: 20rpx 30rpx;
    background-color: #fff;
    box-shadow: 0 -2rpx 12rpx rgba(0, 0, 0, 0.05);

    .submit-btn {
      width: 100%;
      height: 88rpx;
      background: #f4511e;
      color: #fff;
      border-radius: 44rpx;
      font-size: 32rpx;
      display: flex;
      align-items: center;
      justify-content: center;

      .iconfont {
        margin-right: 12rpx;
        font-size: 36rpx;

        &.loading-icon {
          animation: rotate 1s linear infinite;
        }
      }

      &.btn-disabled {
        background: #c8c9cc;
        opacity: 0.8;
      }
    }
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
